# Load Node.js setup function and execute it (silently for commit-msg)
. "$(dirname -- "$0")/_/setup-node.sh"
setup_node > /dev/null 2>&1

# Read the commit message
commit_message=$(cat "$1")

# Check if commit message is not empty
if [ -z "$commit_message" ]; then
  echo "❌ Commit message cannot be empty"
  exit 1
fi

# Check minimum length
if [ ${#commit_message} -lt 3 ]; then
  echo "❌ Commit message must be at least 3 characters long"
  echo "Current message: '$commit_message'"
  exit 1
fi

# Check if commit message starts with a capital letter (DISABLED)
# first_char=$(echo "$commit_message" | cut -c1)
# if ! echo "$first_char" | grep -q '[A-Z]'; then
#   echo "❌ Commit message should start with a capital letter"
#   echo "Current message: '$commit_message'"
#   exit 1
# fi

echo "✅ Commit message validation passed!"
