# Load Node.js setup function and execute it
. "$(dirname -- "$0")/_/setup-node.sh"
setup_node

echo "🔍 Running pre-commit checks..."

# Run type checking
echo "📝 Type checking..."
pnpm type-check
if [ $? -ne 0 ]; then
  echo "❌ Type checking failed. Please fix TypeScript errors before committing."
  exit 1
fi

# Run linting and formatting on staged files
echo "🎨 Formatting and linting staged files..."
pnpm lint-staged
if [ $? -ne 0 ]; then
  echo "❌ Linting/formatting failed. Please fix the issues before committing."
  exit 1
fi

echo "✅ Pre-commit checks passed!"
