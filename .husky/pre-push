#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Pre-push hook para validar tags antes do push
# Este hook é executado antes de fazer push de tags

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Read from stdin (git provides remote and local refs)
while read local_ref local_sha remote_ref remote_sha; do
    # Check if this is a tag push
    if [[ "$remote_ref" == refs/tags/* ]]; then
        tag_name=$(echo "$remote_ref" | sed 's|refs/tags/||')

        print_info "🏷️  Validando tag: $tag_name"

        # Check if tag follows Lisa pattern
        if [[ "$tag_name" =~ ^@lisa/([^@]+)@(.+)$ ]]; then
            project_name="${BASH_REMATCH[1]}"
            tag_version="${BASH_REMATCH[2]}"

            print_info "📦 Projeto: $project_name"
            print_info "🔢 Versão: $tag_version"

            # Check if project exists and has package.json
            project_dir="apps/$project_name"
            package_json="$project_dir/package.json"

            if [ ! -d "$project_dir" ]; then
                print_error "Diretório do projeto não encontrado: $project_dir"
                print_error "Push da tag cancelado!"
                exit 1
            fi

            if [ ! -f "$package_json" ]; then
                print_error "Package.json não encontrado: $package_json"
                print_error "Push da tag cancelado!"
                exit 1
            fi

            # Validate tag against package.json
            if ! node scripts/validate-tag.js "$tag_name" "$project_dir" 2>/dev/null; then
                print_error "Validação da tag falhou!"
                print_error "A tag não corresponde ao nome/versão no package.json"
                print_info "💡 Execute: pnpm test:tag-validation:$project_name"
                print_error "Push da tag cancelado!"
                exit 1
            fi

            print_success "Tag válida: $tag_name"

        elif [[ "$tag_name" =~ ^@lisa/ ]]; then
            print_warning "Tag com padrão Lisa mas formato incorreto: $tag_name"
            print_info "💡 Formato esperado: @lisa/{projeto}@{versão}"
            print_warning "Esta tag disparará pipeline de fallback (não fará deploy)."
            print_info "Continuar mesmo assim? (y/N)"
            read -r response
            if [[ ! "$response" =~ ^[Yy]$ ]]; then
                print_error "Push da tag cancelado pelo usuário!"
                exit 1
            fi
        else
            # Check if tag might be related to Lisa projects
            if [[ "$tag_name" =~ ai\.api|twilio\.api|smarters\.api ]]; then
                print_warning "Tag contém nome de projeto Lisa: $tag_name"
                print_info "💡 Formato correto seria: @lisa/{projeto}@{versão}"
                print_warning "Esta tag disparará pipeline de fallback com orientações."
                print_info "Deseja continuar ou cancelar para corrigir? (y/N)"
                read -r response
                if [[ ! "$response" =~ ^[Yy]$ ]]; then
                    print_error "Push da tag cancelado pelo usuário!"
                    exit 1
                fi
            else
                print_info "Tag não relacionada ao projeto Lisa: $tag_name"
                print_info "Esta tag disparará pipeline de fallback informativa."
                print_info "Continuar? (Y/n)"
                read -r response
                if [[ "$response" =~ ^[Nn]$ ]]; then
                    print_error "Push da tag cancelado pelo usuário!"
                    exit 1
                fi
            fi
        fi
    fi
done

print_success "Validação de tags concluída!"
