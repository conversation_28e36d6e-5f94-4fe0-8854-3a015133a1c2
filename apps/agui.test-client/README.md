# AG-UI Test Client

Um cliente de teste Node.js/NestJS que implementa o protocolo AG-UI de forma nativa usando `@ag-ui/client` diretamente.

## 🎯 Objetivo

Este projeto testa o protocolo AG-UI de forma mais nativa, usando `@ag-ui/client` diretamente e se comunicando com o serviço `ai.api` (Mastra) existente no workspace.

## 🏗️ Arquitetura

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│  copilotkit.test-   │    │  agui.test-client   │    │      ai.api         │
│     client          │    │   (este projeto)    │◄──►│   (Mastra/OpenAI)   │
│   (React/Vite)      │    │   (NestJS/AG-UI)    │    │   Port: 4011        │
│   Port: 5173        │    │   Port: 4013        │    │                     │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## 🚀 Funcionalidades

- ✅ **Protocolo AG-UI Nativo**: Usa `@ag-ui/client` diretamente
- ✅ **Streaming de Eventos**: Server-Sent Events (SSE) para comunicação em tempo real
- ✅ **Integração com ai.api**: Usa o serviço Mastra existente para IA
- ✅ **Tool Calling**: Suporte para chamadas de ferramentas
- ✅ **API REST**: Endpoints HTTP para teste e integração
- ✅ **Fallback Mode**: Funciona com mock se ai.api não estiver disponível

## 📋 Endpoints

| Método | Endpoint                 | Descrição               |
| ------ | ------------------------ | ----------------------- |
| `GET`  | `/health`                | Health check do serviço |
| `GET`  | `/`                      | Informações do serviço  |
| `POST` | `/agui/chat`             | Chat com agent AG-UI    |
| `GET`  | `/agui/stream/:threadId` | Stream de eventos SSE   |
| `POST` | `/agui/tools/call`       | Teste de tool calling   |

## 🛠️ Setup

### 1. Instalar dependências

```bash
# Na raiz do lisa.workspace
npm install
```

### 2. Configurar variáveis de ambiente

```bash
# Copiar arquivo de exemplo
cp apps/agui.test-client/.env.example apps/agui.test-client/.env

# Editar com suas configurações
nano apps/agui.test-client/.env
```

### 3. Configurar no nest-cli.json

Adicionar ao `nest-cli.json`:

```json
{
  "projects": {
    "agui.test-client": {
      "type": "application",
      "root": "apps/agui.test-client",
      "entryFile": "main",
      "sourceRoot": "apps/agui.test-client/src",
      "compilerOptions": {
        "tsConfigPath": "apps/agui.test-client/tsconfig.app.json"
      }
    }
  }
}
```

### 4. Adicionar scripts ao package.json

```json
{
  "scripts": {
    "start:agui.test-client": "nest start agui.test-client",
    "start:agui.test-client:dev": "nest start agui.test-client --watch",
    "start:agui.test-client:prod": "node dist/apps/agui.test-client/main"
  }
}
```

## 🚀 Executar

### Desenvolvimento

```bash
# Iniciar o servidor AG-UI Test Client
npm run start:agui.test-client:dev

# Ou usando env-cmd se tiver arquivo .env
env-cmd -f apps/agui.test-client/.env npm run start:agui.test-client:dev
```

### Produção

```bash
# Build
npm run build

# Start
npm run start:agui.test-client:prod
```

## 🧪 Testes

### Teste básico de saúde

```bash
curl http://localhost:4012/health
```

### Teste de chat

```bash
curl -X POST http://localhost:4012/agui/chat \
  -H "Content-Type: application/json" \
  -d '{
    "threadId": "test-123",
    "messages": [
      {
        "role": "user",
        "content": "Hello! Test the AG-UI protocol."
      }
    ]
  }'
```

### Teste de streaming (SSE)

```bash
curl -N -H "Accept: text/event-stream" \
  http://localhost:4012/agui/stream/test-thread-123
```

### Teste de tool calling

```bash
curl -X POST http://localhost:4012/agui/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "toolName": "get_weather",
    "arguments": {
      "location": "São Paulo",
      "unit": "celsius"
    }
  }'
```

## 🔗 Integração com copilotkit.test-client

Para testar a integração com o projeto React existente:

1. **Iniciar o AG-UI Test Client**:

   ```bash
   npm run start:agui.test-client:dev
   ```

2. **Iniciar o CopilotKit Test Client**:

   ```bash
   npm run start:copitolikit.test-client:dev
   ```

3. **Configurar o frontend** para usar este servidor:
   - URL: `http://localhost:4012`
   - Endpoints disponíveis para integração

## 📊 Eventos AG-UI Suportados

| Evento               | Descrição                         |
| -------------------- | --------------------------------- |
| `RUN_STARTED`        | Início da execução do agent       |
| `RUN_FINISHED`       | Fim da execução do agent          |
| `RUN_ERROR`          | Erro na execução                  |
| `TEXT_MESSAGE_START` | Início de mensagem de texto       |
| `TEXT_MESSAGE_CHUNK` | Chunk de conteúdo de texto        |
| `TEXT_MESSAGE_END`   | Fim de mensagem de texto          |
| `TOOL_CALL_START`    | Início de chamada de ferramenta   |
| `TOOL_CALL_CHUNK`    | Chunk de argumentos da ferramenta |
| `TOOL_CALL_END`      | Fim de chamada de ferramenta      |

## 🐛 Debug

### Logs detalhados

O serviço produz logs detalhados para debug:

```
🚀 AG-UI Test Client is running on: http://localhost:4012
💬 Received chat request: { threadId: 'test-123', messagesCount: 1 }
🚀 Starting AG-UI agent run: { threadId: 'test-123', runId: 'run-1234567890' }
📡 AG-UI Event: RUN_STARTED
📡 AG-UI Event: TEXT_MESSAGE_CHUNK
📡 AG-UI Event: RUN_FINISHED
```

### Modo Fallback

Se o serviço ai.api não estiver disponível, o serviço funciona em modo fallback:

```
🎭 Falling back to mock response...
```

## 🔧 Desenvolvimento

### Estrutura do projeto

```
src/
├── main.ts                      # Bootstrap da aplicação
├── agui.test-client.module.ts   # Módulo principal
├── agui.test-client.controller.ts # Controller HTTP
├── agui.test-client.service.ts  # Serviço principal
├── services/
│   └── agui-agent.service.ts    # Implementação do AG-UI Agent (integra com ai.api)
└── dto/
    └── agui.dto.ts              # Data Transfer Objects
```

### Extensões possíveis

- [ ] Suporte a mais modelos (Anthropic, Ollama)
- [ ] Persistência de conversas
- [ ] Métricas e observabilidade
- [ ] WebSocket além de SSE
- [ ] Autenticação e autorização
- [ ] Rate limiting

## 📚 Referências

- [AG-UI Protocol](https://docs.ag-ui.com/)
- [AG-UI GitHub](https://github.com/ag-ui-protocol/ag-ui)
- [NestJS Documentation](https://docs.nestjs.com/)
- [Mastra Framework](https://mastra.ai/)
