<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AG-UI Test Client - Demo</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #f5f5f5;
      }
      .container {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
      }
      h2 {
        color: #666;
        border-bottom: 2px solid #eee;
        padding-bottom: 10px;
      }
      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #0056b3;
      }
      button:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
      .output {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 15px;
        margin: 10px 0;
        white-space: pre-wrap;
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 12px;
        max-height: 300px;
        overflow-y: auto;
      }
      .status {
        padding: 10px;
        border-radius: 4px;
        margin: 10px 0;
      }
      .status.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .status.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .status.info {
        background: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      input,
      textarea {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin: 5px 0;
      }
      textarea {
        height: 100px;
        resize: vertical;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🤖 AG-UI Test Client Demo</h1>
      <p>
        Este é um cliente de teste para o protocolo AG-UI usando
        <code>@ag-ui/client</code> diretamente.
      </p>

      <div class="status info">
        <strong>Servidor:</strong> <span id="serverStatus">Verificando...</span>
      </div>
    </div>

    <div class="container">
      <h2>🏥 Health Check</h2>
      <button onclick="checkHealth()">Verificar Saúde do Serviço</button>
      <div id="healthOutput" class="output"></div>
    </div>

    <div class="container">
      <h2>💬 Chat com AG-UI Agent</h2>
      <textarea id="chatMessage" placeholder="Digite sua mensagem aqui...">
Olá! Teste o protocolo AG-UI por favor.</textarea
      >
      <br />
      <button onclick="sendChat()">Enviar Chat</button>
      <div id="chatOutput" class="output"></div>
    </div>

    <div class="container">
      <h2>🌊 Stream de Eventos (SSE)</h2>
      <input
        type="text"
        id="threadId"
        placeholder="Thread ID (opcional)"
        value="demo-thread-123"
      />
      <br />
      <button onclick="startStream()" id="streamBtn">Iniciar Stream</button>
      <button onclick="stopStream()" id="stopBtn" disabled>Parar Stream</button>
      <div id="streamOutput" class="output"></div>
    </div>

    <div class="container">
      <h2>🔧 Tool Calling</h2>
      <input
        type="text"
        id="toolName"
        placeholder="Nome da ferramenta"
        value="get_weather"
      />
      <textarea id="toolArgs" placeholder="Argumentos JSON">
{"location": "São Paulo", "unit": "celsius"}</textarea
      >
      <button onclick="callTool()">Chamar Ferramenta</button>
      <div id="toolOutput" class="output"></div>
    </div>

    <script>
      const API_BASE = 'http://localhost:4014';
      let eventSource = null;

      // Check server status on load
      window.onload = function () {
        checkServerStatus();
      };

      async function checkServerStatus() {
        try {
          const response = await fetch(`${API_BASE}/health`);
          const data = await response.json();
          document.getElementById('serverStatus').textContent =
            `✅ Online - ${data.service} (${data.status})`;
        } catch (error) {
          document.getElementById('serverStatus').textContent =
            `❌ Offline - ${error.message}`;
        }
      }

      async function checkHealth() {
        const output = document.getElementById('healthOutput');
        output.textContent = 'Verificando saúde do serviço...';

        try {
          const response = await fetch(`${API_BASE}/health`);
          const data = await response.json();
          output.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          output.textContent = `Erro: ${error.message}`;
        }
      }

      async function sendChat() {
        const output = document.getElementById('chatOutput');
        const message = document.getElementById('chatMessage').value;

        if (!message.trim()) {
          output.textContent = 'Por favor, digite uma mensagem.';
          return;
        }

        output.textContent = 'Enviando mensagem...';

        try {
          const response = await fetch(`${API_BASE}/agui/chat`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              threadId: `chat-${Date.now()}`,
              messages: [
                {
                  role: 'user',
                  content: message,
                },
              ],
            }),
          });

          const data = await response.json();
          output.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          output.textContent = `Erro: ${error.message}`;
        }
      }

      function startStream() {
        const output = document.getElementById('streamOutput');
        const threadId =
          document.getElementById('threadId').value || 'demo-thread-123';

        if (eventSource) {
          eventSource.close();
        }

        output.textContent = 'Conectando ao stream...\n';

        eventSource = new EventSource(`${API_BASE}/agui/stream/${threadId}`);

        eventSource.onopen = function () {
          output.textContent += '✅ Stream conectado!\n';
          document.getElementById('streamBtn').disabled = true;
          document.getElementById('stopBtn').disabled = false;
        };

        eventSource.onmessage = function (event) {
          const data = JSON.parse(event.data);
          output.textContent += `📡 ${data.type}: ${JSON.stringify(data)}\n`;
          output.scrollTop = output.scrollHeight;
        };

        eventSource.onerror = function (error) {
          output.textContent += `❌ Erro no stream: ${error}\n`;
          stopStream();
        };
      }

      function stopStream() {
        if (eventSource) {
          eventSource.close();
          eventSource = null;
        }

        document.getElementById('streamOutput').textContent +=
          '🔌 Stream desconectado.\n';
        document.getElementById('streamBtn').disabled = false;
        document.getElementById('stopBtn').disabled = true;
      }

      async function callTool() {
        const output = document.getElementById('toolOutput');
        const toolName = document.getElementById('toolName').value;
        const toolArgsText = document.getElementById('toolArgs').value;

        if (!toolName.trim()) {
          output.textContent = 'Por favor, digite o nome da ferramenta.';
          return;
        }

        let toolArgs;
        try {
          toolArgs = JSON.parse(toolArgsText || '{}');
        } catch (error) {
          output.textContent = `Erro no JSON dos argumentos: ${error.message}`;
          return;
        }

        output.textContent = 'Chamando ferramenta...';

        try {
          const response = await fetch(`${API_BASE}/agui/tools/call`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              toolName: toolName,
              arguments: toolArgs,
            }),
          });

          const data = await response.json();
          output.textContent = JSON.stringify(data, null, 2);
        } catch (error) {
          output.textContent = `Erro: ${error.message}`;
        }
      }

      // Cleanup on page unload
      window.onbeforeunload = function () {
        if (eventSource) {
          eventSource.close();
        }
      };
    </script>
  </body>
</html>
