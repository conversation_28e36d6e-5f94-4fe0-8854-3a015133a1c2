import { Body, Controller, Get, Post } from '@nestjs/common';
import { AguiTestServerService } from './agui.test-server.service';
import { AguiAgentService } from './services/agui-agent.service';

@Controller()
export class AguiTestServerController {
  constructor(
    private readonly aguiTestServerService: AguiTestServerService,
    private readonly aguiAgentService: AguiAgentService,
  ) {}

  @Get('health')
  getHealth() {
    return this.aguiTestServerService.getHealth();
  }

  @Get()
  getServiceInfo() {
    return this.aguiTestServerService.getServiceInfo();
  }

  @Post('chat')
  async sendMessage(@Body() body: { threadId: string; message: string }) {
    if (!body.threadId || !body.message) {
      return {
        error: 'threadId and message are required',
        timestamp: new Date().toISOString(),
      };
    }

    return await this.aguiAgentService.sendMessage(body.threadId, body.message);
  }
}
