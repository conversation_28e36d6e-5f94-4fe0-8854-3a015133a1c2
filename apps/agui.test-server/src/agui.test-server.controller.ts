import { Body, Controller, Get, Post } from '@nestjs/common';
import { AguiTestClientService } from './agui.test-client.service';
import { AguiAgentService } from './services/agui-agent.service';

@Controller()
export class AguiTestClientController {
  constructor(
    private readonly aguiTestClientService: AguiTestClientService,
    private readonly aguiAgentService: AguiAgentService,
  ) {}

  @Get('health')
  getHealth() {
    return this.aguiTestClientService.getHealth();
  }

  @Get()
  getServiceInfo() {
    return this.aguiTestClientService.getServiceInfo();
  }

  @Post('chat')
  async sendMessage(@Body() body: { threadId: string; message: string }) {
    if (!body.threadId || !body.message) {
      return {
        error: 'threadId and message are required',
        timestamp: new Date().toISOString(),
      };
    }

    return await this.aguiAgentService.sendMessage(body.threadId, body.message);
  }
}
