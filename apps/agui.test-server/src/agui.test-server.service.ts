import { Injectable } from '@nestjs/common';

@Injectable()
export class AguiTestClientService {
  getHealth(): { status: string; timestamp: string; service: string } {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'Minimal AG-UI Test Client',
    };
  }

  getServiceInfo() {
    return {
      name: 'Minimal AG-UI Test Client',
      version: '0.0.1',
      description: 'Minimal AG-UI Test Client using @mastra/client-js',
      endpoints: {
        health: 'GET /health',
        connection: 'GET /test/connection',
        chat: 'POST /chat',
      },
      dependencies: {
        '@mastra/client-js': '^0.1.50',
      },
      integrations: {
        'ai.api': 'http://localhost:4011 (Mastra service)',
      },
    };
  }
}
