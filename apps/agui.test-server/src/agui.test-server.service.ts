import { Injectable } from '@nestjs/common';

@Injectable()
export class AguiTestServerService {
  getHealth(): { status: string; timestamp: string; service: string } {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'Minimal AG-UI Test Server',
    };
  }

  getServiceInfo() {
    return {
      name: 'Minimal AG-UI Test Server',
      version: '0.0.1',
      description: 'Minimal AG-UI Test Server using @mastra/client-js',
      endpoints: {
        health: 'GET /health',
        connection: 'GET /test/connection',
        chat: 'POST /chat',
      },
      dependencies: {
        '@mastra/client-js': '^0.1.50',
      },
      integrations: {
        'ai.api': 'http://localhost:4011 (Mastra service)',
      },
    };
  }
}
