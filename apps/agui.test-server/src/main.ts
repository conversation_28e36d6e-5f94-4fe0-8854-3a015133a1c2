import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { AguiTestClientModule } from './agui.test-client.module';

async function bootstrap() {
  const app =
    await NestFactory.create<NestExpressApplication>(AguiTestClientModule);

  // Serve static files from public directory
  const publicPath = join(process.cwd(), 'apps', 'agui.test-client', 'public');
  app.useStaticAssets(publicPath);

  // Enable CORS for frontend communication
  app.enableCors({
    origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-User-ID'],
  });

  const port = process.env.PORT || 4013;
  await app.listen(port);

  console.log(
    `🚀 Minimal AG-UI Test Client is running on: http://localhost:${port}`,
  );
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  /health - Health check`);
  console.log(`   GET  / - Service info`);
  console.log(`   POST /chat - Send message to Mastra agent`);
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start AG-UI Test Client:', error);
  process.exit(1);
});
