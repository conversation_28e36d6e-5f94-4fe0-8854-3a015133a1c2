import { MastraClient } from '@mastra/client-js';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AguiAgentService {
  private readonly mastraApiUrl: string;
  private mastraClient: MastraClient;
  private agentCache: Map<string, any> = new Map();

  constructor() {
    this.mastraApiUrl = process.env.AI_API_URL || 'http://localhost:4011';
    console.log(`🎯 AG-UI Agent at: ${this.mastraApiUrl}`);

    this.mastraClient = new MastraClient({
      baseUrl: this.mastraApiUrl,
    });
  }

  private async getOrCreateAgent(threadId: string): Promise<any> {
    if (this.agentCache.has(threadId)) {
      return this.agentCache.get(threadId);
    }

    const aguiAdapters = await this.mastraClient.getAGUI({
      resourceId: 'generalConversationAgent',
    });

    const baseAGUIAdapter = (aguiAdapters as any).generalConversationAgent;
    if (!baseAGUIAdapter) {
      throw new Error('generalConversationAgent not found in AGUI');
    }

    baseAGUIAdapter.threadId = threadId;
    this.agentCache.set(threadId, baseAGUIAdapter);

    return baseAGUIAdapter;
  }

  async sendMessage(
    threadId: string,
    message: string,
  ): Promise<{ success: boolean; response?: string }> {
    try {
      const agent = await this.getOrCreateAgent(threadId);

      const userMessage = {
        role: 'user',
        content: message,
      };

      agent.messages.push(userMessage);

      const runId = `run-${threadId}-${Date.now()}`;
      await agent.runAgent({
        runId,
        threadId,
      });

      // Get the latest assistant message from agent's memory
      const assistantMessages = agent.messages.filter(
        (msg: any) => msg.role === 'assistant',
      );
      const lastResponse = assistantMessages[assistantMessages.length - 1];

      return {
        success: true,
        response: lastResponse?.content || 'No response generated',
      };
    } catch (error: any) {
      console.error('❌ Error in sendMessage:', error);
      return {
        success: false,
      };
    }
  }
}
