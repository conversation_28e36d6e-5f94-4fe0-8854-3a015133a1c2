import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AguiTestClientModule } from '../src/agui.test-client.module';

describe('AguiTestClientController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AguiTestClientModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  it('/health (GET)', () => {
    return request(app.getHttpServer())
      .get('/health')
      .expect(200)
      .expect((res) => {
        expect(res.body.status).toBe('healthy');
        expect(res.body.service).toBe('AG-UI Test Client');
      });
  });

  it('/ (GET)', () => {
    return request(app.getHttpServer())
      .get('/')
      .expect(200)
      .expect((res) => {
        expect(res.body.name).toBe('AG-UI Test Client');
        expect(res.body.version).toBe('0.0.1');
      });
  });

  it('/agui/chat (POST)', () => {
    const chatRequest = {
      threadId: 'test-thread-123',
      messages: [
        {
          role: 'user',
          content: 'Hello, test the AG-UI protocol!',
        },
      ],
    };

    return request(app.getHttpServer())
      .post('/agui/chat')
      .send(chatRequest)
      .expect(201)
      .expect((res) => {
        expect(res.body.success).toBe(true);
        expect(res.body.threadId).toBe('test-thread-123');
        expect(res.body.events).toBeDefined();
        expect(Array.isArray(res.body.events)).toBe(true);
      });
  });

  it('/agui/tools/call (POST)', () => {
    const toolRequest = {
      toolName: 'test_tool',
      arguments: { param1: 'value1', param2: 42 },
    };

    return request(app.getHttpServer())
      .post('/agui/tools/call')
      .send(toolRequest)
      .expect(201)
      .expect((res) => {
        expect(res.body.success).toBe(true);
        expect(res.body.toolCall.toolName).toBe('test_tool');
        expect(res.body.toolCall.success).toBe(true);
      });
  });

  it('/openai/test (GET)', () => {
    return request(app.getHttpServer())
      .get('/openai/test')
      .expect(200)
      .expect((res) => {
        expect(res.body.success).toBeDefined();
        expect(res.body.message).toBeDefined();
      });
  });
});
