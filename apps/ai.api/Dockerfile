# Multi-stage build for ai.api (Mastra-based)
FROM node:22-alpine AS builder

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/ai.api/package.json ./apps/ai.api/
COPY libs/ ./libs/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code and config files
COPY apps/ai.api/ ./apps/ai.api/
# Copy any additional config files that might be needed
COPY turbo.json ./
COPY tsconfig.json ./

# Build the application using Mastra
WORKDIR /app/apps/ai.api
RUN MASTRA_TELEMETRY_DISABLED=1 pnpm build

# Production stage
FROM node:22-alpine AS production

WORKDIR /app

# Copy built application from builder stage
COPY --from=builder /app/apps/ai.api/.mastra/output/ .

# Expose port (Mastra default is 4111)
EXPOSE 4111

# Health check for Mastra API
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:4111/api/workflows', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Start the application using Mastra's generated output
CMD ["node", "--import=./instrumentation.mjs", "index.mjs"]
