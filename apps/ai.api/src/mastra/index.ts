import {
  generalConversationAgent,
  intentAgent,
  leadWorkflow,
} from '@lisa/lead-workflow';
import { Ma<PERSON> } from '@mastra/core';
import { LibSQLStore } from '@mastra/libsql';
import { PinoLogger } from '@mastra/loggers';
import 'dotenv/config';

export const mastra = new Mastra({
  agents: { intentAgent, generalConversationAgent },
  workflows: { leadWorkflow },
  storage: new LibSQLStore({
    url: 'file:../mastra.db',
  }),
  logger: new PinoLogger({
    name: 'Mastra AI API',
    level: 'info',
  }),
});
