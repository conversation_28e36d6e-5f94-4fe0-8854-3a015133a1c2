# AI Test Server

NestJS API for testing AI workflows via Mastra Client SDK.

## Prerequisites

This application requires the `ai.api` to be running, as it connects to it via the Mastra Client SDK to execute workflows.

## Setup

1. Copy the environment file:

   ```bash
   cp .env.example .env
   ```

2. Configure the environment variables in `.env`:
   - `PORT`: Port for the test server (default: 4012)
   - `AI_API_URL`: URL where the ai.api is running (default: http://localhost:4111)
   - `OPENAI_API_KEY`: OpenAI API key (if needed by the workflow)

## Usage

### Start the ai.api first

Before running this test server, make sure the ai.api is running:

```bash
# From the workspace root
pnpm dev:ai.api
```

### Run the test server

```bash
# Development mode
pnpm dev:ai.test-server

# Production mode
pnpm build
pnpm start:prod
```

## API Endpoints

### GET /health
Health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "AI Test Server"
}
```

### GET /
Service information and available endpoints.

### GET /check-api
Check if ai.api is running and accessible.

**Response:**
```json
{
  "status": "healthy",
  "apiUrl": "http://localhost:4111",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "message": "ai.api is running and accessible"
}
```

### POST /test-workflow
Execute the leadWorkflow with test data.

**Request Body (optional):**
```json
{
  "text": "Custom message for the workflow",
  "fromCrmPerson": {
    "name": {
      "firstName": "Custom",
      "lastName": "Name"
    },
    "phones": {
      "primaryPhoneCountryCode": "55",
      "primaryPhoneNumber": "21999999999"
    }
  }
}
```

**Response:**
```json
{
  "success": true,
  "timestamp": "2024-01-01T00:00:00.000Z",
  "workflow": "leadWorkflow",
  "inputData": { ... },
  "result": { ... }
}
```

## How it works

1. **Health Check**: Provides endpoints to check both server and ai.api health
2. **Connection**: Connects to the ai.api using the Mastra Client SDK
3. **Workflow Access**: Gets the `leadWorkflow` from the running ai.api
4. **Execution**: Creates a new workflow run with a unique ID
5. **Processing**: Starts the workflow with test data (lead information and message)
6. **Results**: Returns the workflow execution results via HTTP response

### Health Check Features

The server provides multiple health check endpoints:

- `/health`: Check if the test server itself is running
- `/check-api`: Verify that ai.api is running and accessible
- Automatic retries and detailed error messages
- Clear troubleshooting guidance in error responses

## Architecture

```
Client → ai.test-server (NestJS API) → ai.api (Mastra Server) → leadWorkflow
```

This approach provides better separation of concerns:

- **Client**: Any HTTP client (Postman, curl, frontend app)
- **ai.test-server**: NestJS API that provides testing endpoints
- **ai.api**: Centralized AI service that hosts workflows and agents
- **leadWorkflow**: Business logic for lead processing

## Development

### Testing with curl

```bash
# Check server health
curl http://localhost:4012/health

# Check ai.api connectivity
curl http://localhost:4012/check-api

# Test workflow with default data
curl -X POST http://localhost:4012/test-workflow

# Test workflow with custom message
curl -X POST http://localhost:4012/test-workflow \
  -H "Content-Type: application/json" \
  -d '{"text": "Olá, gostaria de informações sobre imóveis"}'
```

### Error Handling

The server provides detailed error responses with troubleshooting information:

- Connection issues with ai.api
- Workflow availability problems
- Configuration errors
- Clear guidance on how to resolve issues
