import {
  Body,
  Controller,
  Get,
  Post,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AiTestServerService } from './ai.test-server.service';
import {
  MastraClientService,
  TestWorkflowInput,
} from './services/mastra-client.service';

@Controller()
export class AiTestServerController {
  constructor(
    private readonly aiTestServerService: AiTestServerService,
    private readonly mastraClientService: MastraClientService,
  ) {}

  @Get('health')
  getHealth() {
    return this.aiTestServerService.getHealth();
  }

  @Get()
  getServiceInfo() {
    return this.aiTestServerService.getServiceInfo();
  }

  @Get('check-api')
  async checkApiHealth() {
    try {
      const isHealthy = await this.mastraClientService.checkApiHealth(3);
      return {
        status: isHealthy ? 'healthy' : 'unhealthy',
        apiUrl: this.mastraClientService.getApiUrl(),
        timestamp: new Date().toISOString(),
        message: isHealthy
          ? 'ai.api is running and accessible'
          : 'ai.api is not responding',
      };
    } catch (error) {
      throw new HttpException(
        {
          status: 'error',
          apiUrl: this.mastraClientService.getApiUrl(),
          timestamp: new Date().toISOString(),
          message: 'Failed to check ai.api health',
          error: error instanceof Error ? error.message : 'Unknown error',
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }

  @Post('test-workflow')
  async testWorkflow(@Body() customInput?: Partial<TestWorkflowInput>) {
    try {
      // Default test data (same as original script)
      const defaultInput: TestWorkflowInput = {
        messageType: 'text',
        messageExternalId: '',
        fromCrmPerson: {
          id: '',
          name: {
            firstName: 'João',
            lastName: 'Silva',
          },
          companyId: null,
          role: 'LEAD',
          communicationExternalId: '',
          phones: {
            primaryPhoneCountryCode: '55',
            primaryPhoneNumber: '21985183012',
          },
        },
        toCrmPerson: {
          id: '',
          name: {
            firstName: 'Lisa',
            lastName: 'By Homer',
          },
          companyId: null,
          role: 'IA',
          communicationExternalId: '',
          phones: {
            primaryPhoneCountryCode: '55',
            primaryPhoneNumber: '21999999999',
          },
        },
        text: 'Olá, estou interessado em comprar um apartamento.',
        messages: [],
      };

      // Merge custom input with defaults
      const inputData = { ...defaultInput, ...customInput };

      const result = await this.mastraClientService.executeWorkflow(
        'leadWorkflow',
        inputData,
      );

      return {
        success: true,
        timestamp: new Date().toISOString(),
        workflow: 'leadWorkflow',
        inputData,
        result,
      };
    } catch (error) {
      throw new HttpException(
        {
          success: false,
          timestamp: new Date().toISOString(),
          workflow: 'leadWorkflow',
          message: 'Failed to execute workflow',
          error: error instanceof Error ? error.message : 'Unknown error',
          troubleshooting: {
            'ai.api not running':
              'Make sure ai.api is started: pnpm dev:ai.api',
            'workflow not ready': 'Wait for ai.api to fully initialize',
            'connection issues': 'Check AI_API_URL environment variable',
          },
        },
        HttpStatus.SERVICE_UNAVAILABLE,
      );
    }
  }
}
