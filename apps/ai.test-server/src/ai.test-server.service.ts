import { Injectable } from '@nestjs/common';

@Injectable()
export class AiTestServerService {
  getHealth(): { status: string; timestamp: string; service: string } {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'AI Test Server',
    };
  }

  getServiceInfo() {
    return {
      name: 'AI Test Server',
      version: '0.0.1',
      description: 'NestJS API for testing AI workflows via Mastra Client SDK',
      endpoints: [
        'GET /health - Health check',
        'GET / - Service info',
        'POST /test-workflow - Test leadWorkflow execution',
        'GET /check-api - Check if ai.api is running',
      ],
      dependencies: {
        'ai.api': 'Required - Must be running on configured URL',
        'mastra-client': 'Used to connect to ai.api workflows',
      },
    };
  }
}
