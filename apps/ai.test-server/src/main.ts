import { NestFactory } from '@nestjs/core';
import { AiTestServerModule } from './ai.test-server.module';
import 'dotenv/config';

async function bootstrap() {
  const app = await NestFactory.create(AiTestServerModule, {
    logger: ['log', 'error', 'warn', 'debug', 'verbose'],
  });

  // Enable CORS for development
  app.enableCors();

  const port = process.env.PORT || 4012;
  await app.listen(port);

  console.log(`🚀 AI Test Server is running on: http://localhost:${port}`);
  console.log(`📋 Available endpoints:`);
  console.log(`   GET  /health - Health check`);
  console.log(`   GET  / - Service info`);
  console.log(`   GET  /check-api - Check if ai.api is running`);
  console.log(`   POST /test-workflow - Test leadWorkflow execution`);
  console.log(`🔗 AI API URL: ${process.env.AI_API_URL || 'http://localhost:4111'}`);
}

bootstrap().catch((error) => {
  console.error('❌ Failed to start AI Test Server:', error);
  process.exit(1);
});
