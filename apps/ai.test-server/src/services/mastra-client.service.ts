import { Injectable, Logger } from '@nestjs/common';
import { MastraClient } from '@mastra/client-js';

export interface TestWorkflowInput {
  messageType?: string;
  messageExternalId?: string;
  fromCrmPerson: {
    id?: string;
    name: {
      firstName: string;
      lastName: string;
    };
    companyId?: string | null;
    role?: string;
    communicationExternalId?: string;
    phones: {
      primaryPhoneCountryCode: string;
      primaryPhoneNumber: string;
    };
  };
  toCrmPerson: {
    id?: string;
    name: {
      firstName: string;
      lastName: string;
    };
    companyId?: string | null;
    role?: string;
    communicationExternalId?: string;
    phones: {
      primaryPhoneCountryCode: string;
      primaryPhoneNumber: string;
    };
  };
  text: string;
  messages?: any[];
}

@Injectable()
export class MastraClientService {
  private readonly logger = new Logger(MastraClientService.name);
  private client: MastraClient;
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.AI_API_URL || 'http://localhost:4111';
    this.client = new MastraClient({
      baseUrl: this.baseUrl,
    });
  }

  async checkApiHealth(maxRetries = 10): Promise<boolean> {
    for (let i = 0; i < maxRetries; i++) {
      try {
        this.logger.log(
          `Checking if ai.api is running at ${this.baseUrl}... (attempt ${i + 1}/${maxRetries})`,
        );

        const response = await fetch(`${this.baseUrl}/api/workflows`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          this.logger.log('ai.api is running and healthy!');
          return true;
        } else {
          this.logger.warn(`ai.api responded with status ${response.status}`);
        }
      } catch (error) {
        this.logger.warn(
          `Failed to connect to ai.api: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
      }

      if (i < maxRetries - 1) {
        this.logger.log('Waiting 3 seconds before retry...');
        await new Promise((resolve) => setTimeout(resolve, 3000));
      }
    }

    this.logger.error('ai.api is not responding after maximum retries');
    return false;
  }

  async waitForWorkflowAvailability(
    workflowName: string,
    maxRetries = 5,
  ): Promise<boolean> {
    for (let i = 0; i < maxRetries; i++) {
      try {
        this.logger.log(
          `Checking if workflow '${workflowName}' is available... (attempt ${i + 1}/${maxRetries})`,
        );
        const workflow = this.client.getWorkflow(workflowName);
        if (workflow) {
          this.logger.log(`Workflow '${workflowName}' is available!`);
          return true;
        }
      } catch (error) {
        this.logger.warn(
          `Workflow not ready yet: ${error instanceof Error ? error.message : 'Unknown error'}`,
        );
        if (i < maxRetries - 1) {
          this.logger.log('Waiting 5 seconds before retry...');
          await new Promise((resolve) => setTimeout(resolve, 5000));
        }
      }
    }
    this.logger.error(
      `Workflow '${workflowName}' is not available after maximum retries`,
    );
    return false;
  }

  async executeWorkflow(
    workflowName: string,
    inputData: TestWorkflowInput,
  ): Promise<any> {
    // Check if ai.api is running
    const isApiHealthy = await this.checkApiHealth();
    if (!isApiHealthy) {
      throw new Error(
        'ai.api is not running. Make sure ai.api is started before testing workflows.',
      );
    }

    // Wait for workflow to be available
    const isWorkflowReady = await this.waitForWorkflowAvailability(
      workflowName,
    );
    if (!isWorkflowReady) {
      throw new Error(
        `Workflow '${workflowName}' is not ready. Check if ai.api is properly configured.`,
      );
    }

    // Get the workflow from the ai.api
    const workflow = this.client.getWorkflow(workflowName);

    // Create a run with a unique ID
    const { runId } = await workflow.createRun();
    this.logger.log(`Created workflow run with ID: ${runId}`);

    // Start the workflow with the input data
    const result = await workflow.startAsync({
      runId,
      inputData,
    });

    this.logger.log(`Workflow status: ${result.status}`);
    return result;
  }

  getApiUrl(): string {
    return this.baseUrl;
  }
}
