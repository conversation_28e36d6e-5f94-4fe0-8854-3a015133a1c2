# AI Test

Simple script for testing AI workflows via Mastra Client SDK.

## Prerequisites

This application requires the `ai.api` to be running, as it connects to it via the Mastra Client SDK to execute workflows.

## Setup

1. Copy the environment file:

   ```bash
   cp .env.example .env
   ```

2. Configure the environment variables in `.env`:
   - `AI_API_URL`: URL where the ai.api is running (default: http://localhost:4111)
   - `OPENAI_API_KEY`: OpenAI API key (if needed by the workflow)

## Usage

### Start the ai.api first

Before running this test, make sure the ai.api is running:

```bash
# From the workspace root
cd apps/ai.api
pnpm start:dev
```

### Run the test

```bash
# Development mode
pnpm start:dev

# With custom message
pnpm start:dev "Olá, gostaria de informações sobre imóveis"

# Production mode
pnpm build
pnpm start
```

## How it works

1. **Health Check**: First checks if ai.api is running and healthy
2. **Connection**: Connects to the ai.api using the Mastra Client SDK
3. **Workflow Access**: Gets the `leadWorkflow` from the running ai.api
4. **Execution**: Creates a new workflow run with a unique ID
5. **Processing**: Starts the workflow with test data (lead information and message)
6. **Results**: Waits for the workflow to complete and displays the results

### Health Check Feature

The script automatically verifies that ai.api is running before attempting to execute workflows. If the API is not available, it will:

- Display a clear error message
- Provide instructions on how to start the ai.api
- Exit gracefully with helpful guidance

## Architecture

```
ai.test (Mastra Client) → ai.api (Mastra Server) → leadWorkflow
```

This approach provides better separation of concerns:

- `ai.test`: Simple client for testing workflows
- `ai.api`: Centralized AI service that hosts workflows and agents
- `leadWorkflow`: Business logic for lead processing
