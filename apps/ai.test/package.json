{"name": "@lisa/ai.test", "version": "0.0.1", "description": "AI Test - Simple script for testing AI workflows via Mastra Client SDK (requires ai.api running)", "private": true, "scripts": {"build": "tsc -p tsconfig.app.json", "start": "node dist/main.js", "dev": "tsx src/main.ts", "start:dev": "tsx src/main.ts", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@mastra/client-js": "^0.10.5", "dotenv": "^16.5.0"}, "devDependencies": {"@types/node": "^22.10.7", "tsx": "^4.19.2", "typescript": "^5.8.3"}}