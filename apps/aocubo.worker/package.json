{"name": "@lisa/aocubo.worker", "version": "0.0.1", "description": "Aocubo Worker - Integration with Aocubo services", "type": "module", "private": true, "scripts": {"build": "nest build", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:prod": "node dist/main.js", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@lisa/common": "workspace:*", "@lisa/crm-sdk": "workspace:*", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/schedule": "^6.0.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/node": "^22.10.7", "jest": "^30.0.2", "ts-jest": "^29.2.5", "typescript": "^5.8.3"}}