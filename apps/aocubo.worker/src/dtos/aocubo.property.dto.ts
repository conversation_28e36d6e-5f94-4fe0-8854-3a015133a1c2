import { Expose, Type, Transform } from 'class-transformer';
export class AoCuboPropertyDto {
  id: number;
  @Expose({ name: 'Nome do empreendimento' })
  nomeDoEmpreendimento: string;
  @Expose({ name: 'Incorporadora' })
  incorporadora: string;
  @Expose({ name: 'Estágio da Obra' })
  estagioDaObra: string;
  @Expose({ name: 'Data de entrega' })
  dataDeEntrega: string;
  @Expose({ name: 'Link do empreendimento' })
  linkDoEmpreendimento: string;
  @Expose({ name: 'Exclusividade' })
  exclusividade: string;
  @Expose({ name: 'Descri<PERSON>' })
  @Transform(({ value }) => {
    try {
      const parsed = JSON.parse(value);
      return Array.isArray(parsed) ? parsed : [];
    } catch {
      return [];
    }
  })
  descricao: DescriptionDto[];
  @Expose({ name: 'Endereço' })
  endereco: string;
  @Expose({ name: 'Attachments' })
  attachments: AttachmentDto[];
  @Expose({ name: 'Unidades' })
  @Type(() => UnityDto)
  unidades: UnityDto[];
  @Expose({ name: 'Caracteristicas' })
  caracteristicas: string[];
}

class DescriptionDto {
  insert: string;
}

export class AttachmentDto {
  url: string;
  tipo: string;
}

class UnityDto {
  id: number;
  tipo: string;
  vagas: number;
  planta: string;
  @Expose({ name: 'preço' })
  preco: number;
  suites: number;
  lavabos: number;
  quartos: number;
  metragem: number;
  banheiros: number;
  @Expose({ name: 'link da unidade' })
  linkDaUnidade: string;
}
