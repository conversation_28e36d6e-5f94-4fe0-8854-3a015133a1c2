{"name": "@lisa/chatwoot.worker", "version": "0.0.1", "description": "Chatwoot Worker - Scheduled tasks and bulk messaging", "private": true, "scripts": {"build": "nest build", "start": "nest start", "dev": "env-cmd -f .env nest start --watch", "start:dev": "env-cmd -f .env nest start --watch", "start:prod": "env-cmd -f .env node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@figuro/chatwoot-sdk": "^1.1.17", "@lisa/common": "workspace:*", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/schedule": "^6.0.0", "dotenv": "^16.5.0", "env-cmd": "10.1.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "typeorm": "^0.3.25"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/node": "^22.10.7", "jest": "^30.0.2", "ts-jest": "^29.2.5", "typescript": "^5.8.3"}}