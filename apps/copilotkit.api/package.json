{"name": "@lisa/copilotkit.api", "version": "0.0.1", "description": "CopilotKit API - AI-powered chat interface backend", "private": true, "scripts": {"build": "nest build", "start": "nest start", "dev": "env-cmd -f .env nest start --watch", "start:dev": "env-cmd -f .env nest start --watch", "start:prod": "env-cmd -f .env node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@copilotkit/runtime": "^1.8.13", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@types/express": "^5.0.3", "env-cmd": "10.1.0", "envalid": "8.0.0", "express": "^5.1.0", "openai": "^4.103.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/node": "^22.10.7", "jest": "^30.0.2", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "typescript": "^5.8.3"}}