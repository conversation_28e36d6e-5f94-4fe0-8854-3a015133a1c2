{"name": "@lisa/smarters.api", "version": "0.0.1", "description": "Smarters API - Integration with Chatwoot and messaging services", "private": true, "scripts": {"build": "nest build", "start": "nest start", "dev": "env-cmd -f .env nest start --watch", "start:dev": "env-cmd -f .env nest start --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@figuro/chatwoot-sdk": "^1.1.17", "@lisa/common": "workspace:*", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "axios": "^1.9.0", "class-transformer": "^0.5.1", "dotenv": "^16.5.0", "env-cmd": "10.1.0", "kafkajs": "^2.2.4", "mime-types": "^3.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/express": "^5.0.0", "@types/mime-types": "^2.1.4", "@types/node": "^22.10.7", "jest": "^30.0.2", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "typescript": "^5.8.3"}}