# WebChat API

O **WebChat API** é um microserviço responsável por processar mensagens de WebChat no ecossistema Lisa. Ele faz parte da arquitetura de microserviços do `lisa.workspace` e fornece comunicação em tempo real via WebSocket.

## 🎯 Funcionalidades

- **Processamento de mensagens WebChat**: Recebe e processa mensagens vindas do canal WebChat
- **Gerenciamento de sessões**: Mantém controle de sessões WebSocket
- **Respostas em tempo real**: Envia respostas de volta para clientes WebChat

## 🏗️ Arquitetura

### **Fluxo de Mensagens**

```
WebChat Client → WebSocket → WebChat API → Processing Logic
```

### **Comunicação WebSocket**

- **Endpoint**: `ws://localhost:3002` - Conexões WebSocket diretas
- **Real-time**: Comunicação bidirecional em tempo real

## 📋 Estrutura

```
src/
├── main.ts                           # Bootstrap do microserviço
├── webchat.api.module.ts             # Módulo principal
├── webchat.api.controller.ts         # Controller para mensagens
├── webchat.api.service.ts            # Lógica de negócio
├── webchat.gateway.ts                # WebSocket Gateway
├── dtos/                             # Data Transfer Objects
│   └── webchat.output.message.dto.ts
└── utils/
    └── messages/
        └── parse-message.pipe.ts     # Parser de mensagens
```

## 🚀 Como usar

### **Desenvolvimento**

```bash
# Instalar dependências
npm install

# Iniciar em modo desenvolvimento
npm run dev:webchat.api

# Build
npm run build
```

### **Produção**

```bash
# Build e start
npm run build
npm run start:prod
```

## 🔧 Configuração

### **Kafka**

- **Brokers**: `localhost:9092`
- **Consumer Group**: `webchat-worker-consumer`

### **Variáveis de Ambiente**

```env
KAFKA_BROKERS=localhost:9092
```

## 📡 Integração

### **Com Clientes WebChat**

O WebChat Worker gerencia conexões WebSocket diretas com clientes.

### **Com Serviços Externos**

Pode ser integrado com outros serviços via Kafka ou APIs REST conforme necessário.

## 🧪 Testes

```bash
# Testes unitários
npm run test

# Testes e2e
npm run test:e2e

# Coverage
npm run test:cov
```

## 📝 DTOs

### **WebchatInputMessageDto**

```typescript
{
  messageType: string;
  socketId: string;
  sessionId?: string;
  text?: string;
  timestamp?: number;
}
```

### **WebchatOutputMessageDto**

```typescript
{
  socketId: string;
  text: string;
  messageType: string;
  timestamp?: number;
}
```
