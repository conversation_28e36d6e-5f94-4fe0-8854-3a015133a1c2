{"name": "@lisa/webchat.api", "version": "0.0.1", "description": "WebChat API - Handles WebSocket connections and real-time messaging", "private": true, "scripts": {"build": "nest build", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@lisa/common": "workspace:*", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-ws": "^11.1.2", "@nestjs/websockets": "^11.1.2", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "ws": "^8.18.2"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/node": "^22.10.7", "@types/ws": "^8.5.13", "jest": "^30.0.2", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "typescript": "^5.8.3"}}