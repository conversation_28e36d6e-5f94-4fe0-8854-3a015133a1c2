export class WebchatInputMessageContactDto {
  externalId?: string;
  socketId: string;
  sessionId?: string;
  profileName?: string;

  constructor(seed: Partial<WebchatInputMessageContactDto>) {
    Object.assign(this, seed);
  }
}

export class WebchatInputMessageDto {
  messageType: string;
  messageExternalId?: string;
  conversationExternalId: string;
  socketId: string;
  sessionId?: string;
  from: WebchatInputMessageContactDto;
  to: WebchatInputMessageContactDto;
  text?: string;
  timestamp?: number;

  constructor(seed: Partial<WebchatInputMessageDto>) {
    Object.assign(this, seed);
  }
}
