import { NestFactory } from '@nestjs/core';
import { WebchatApiModule } from './webchat.api.module';
// import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { WsAdapter } from '@nestjs/platform-ws';

async function bootstrap() {
  // Create WebSocket application (Kaf<PERSON> removed)
  const app = await NestFactory.create(WebchatApiModule, {
    logger: ['error', 'warn', 'log'],
  });

  // Use WebSocket adapter for pure WebSocket support
  app.useWebSocketAdapter(new WsAdapter(app));

  // TODO: Replace Kafka with alternative messaging system
  // Add Kafka microservice to the same application
  // app.connectMicroservice<MicroserviceOptions>({
  //   transport: Transport.KAFKA,
  //   options: {
  //     client: {
  //       brokers: ['localhost:9092'],
  //     },
  //     consumer: {
  //       groupId: 'webchat-worker-consumer',
  //     },
  //   },
  // });

  // Start microservices (Kafka)
  // await app.startAllMicroservices();

  // Start WebSocket server on port 3002
  await app.listen(3002);

  // eslint-disable-next-line no-console
  console.log('WebChat API is listening: WebSocket (port 3002)');
}
void bootstrap();
