import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common';
import { WebchatInputMessageDto } from '@lisa/common';

@Injectable()
export class ParseMessagePipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata): WebchatInputMessageDto {
    if (typeof value === 'string') {
      try {
        const parsed = JSON.parse(value);
        return new WebchatInputMessageDto(parsed);
      } catch (error) {
        throw new Error('Invalid JSON format for WebChat message');
      }
    }
    return new WebchatInputMessageDto(value);
  }
}
