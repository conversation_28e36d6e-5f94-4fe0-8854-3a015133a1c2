import { Controller } from '@nestjs/common';
import { WebchatApiService } from './webchat.api.service';
// import { MessagePattern, Payload } from '@nestjs/microservices';
// import { WebchatInputMessageDto } from '@lisa/common';
// import { ParseMessagePipe } from './utils/messages/parse-message.pipe';

@Controller()
export class WebchatApiController {
  constructor(private readonly webchatApiService: WebchatApiService) {}

  // TODO: Replace Kafka message patterns with alternative messaging system
  // @MessagePattern('webchat.input')
  // async handleWebchatInput(
  //   @Payload(new ParseMessagePipe()) message: WebchatInputMessageDto,
  // ): Promise<void> {
  //   return this.webchatApiService.handleInput(message);
  // }

  // @MessagePattern('webchat.output')
  // async handleWebchatOutput(@Payload() message: any): Promise<void> {
  //   console.log('WebChat output message received:', message);
  //   // Handle outbound messages to WebChat clients
  //   if (message.socketId && message.text) {
  //     await this.webchatApiService.sendMessage(
  //       message.socketId,
  //       message.text,
  //     );
  //   }
  // }
}
