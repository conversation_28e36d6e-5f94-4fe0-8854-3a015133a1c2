// import { WebchatInputMessageDto } from '@lisa/common';
import { Injectable } from '@nestjs/common';
// import { WebchatOutputMessageDto } from './dtos/webchat.output.message.dto';

@Injectable()
export class WebchatApiService {
  constructor() {}

  // TODO: Replace Kafka input handling with alternative messaging system
  // async handleInput(input: WebchatInputMessageDto): Promise<void> {
  //   console.log('WebChat Worker received input:', input);

  //   try {
  //     // Process the WebChat message
  //     // This is where you would implement WebChat-specific logic
  //     // For example: validate message, transform data, etc.

  //     // Create response message
  //     const responseMessage = new WebchatOutputMessageDto({
  //       socketId: input.socketId,
  //       sessionId: input.sessionId,
  //       text: `Echo: ${input.text}`, // Example response
  //       messageType: 'text',
  //       timestamp: Date.now(),
  //     });

  //     // TODO: Implement message handling logic
  //     // For now, just log the response
  //     console.log('Response message:', responseMessage);

  //     console.log('WebChat Worker processed message successfully');
  //   } catch (error) {
  //     console.error('Error processing WebChat message:', error);
  //     throw error;
  //   }
  // }

  // TODO: Replace Kafka-based message sending with direct WebSocket communication
  // async sendMessage(socketId: string, message: string): Promise<void> {
  //   // This method would handle sending messages to WebChat clients
  //   // Implementation would depend on how WebSocket connections are managed
  //   console.log(`Sending message to WebChat socket ${socketId}: ${message}`);

  //   // Here you would typically:
  //   // 1. Find the WebSocket connection by socketId
  //   // 2. Send the message through the WebSocket
  //   // 3. Handle any errors or connection issues
  // }

  async handleWebSocketMessage(inputMessage: any): Promise<string> {
    // eslint-disable-next-line no-console
    console.log('WebChat Worker received WebSocket message:', inputMessage);

    try {
      // TODO: Implement message processing logic
      // eslint-disable-next-line no-console
      console.log('Processing WebSocket message...');

      // For now, return a simple response while we wait for Orchestrator integration
      const response = `Echo: ${inputMessage.message}`;

      // eslint-disable-next-line no-console
      console.log('WebChat Worker processed WebSocket message successfully');
      return response;
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error processing WebSocket message:', error);
      throw error;
    }
  }
}
