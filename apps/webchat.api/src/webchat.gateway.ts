import { Logger } from '@nestjs/common';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import * as WebSocket from 'ws';
import { WebchatApiService } from './webchat.api.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class WebchatGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: WebSocket.Server;

  private readonly logger = new Logger(WebchatGateway.name);

  constructor(private readonly webchatApiService: WebchatApiService) {}

  handleConnection(client: WebSocket, request: any) {
    const url = new URL(request.url, 'ws://localhost:3002');
    const sessionId = url.searchParams.get('sessionId');

    this.logger.log(`Client connected: sessionId: ${sessionId}`);

    // Store sessionId in client
    (client as any).sessionId = sessionId;

    // Add message listener for processing
    client.on('message', async (data) => {
      this.logger.log(`Raw message received: ${data.toString()}`);
      await this.processMessage(data.toString(), client);
    });

    // Send connection confirmation
    client.send(
      JSON.stringify({
        type: 'connection',
        status: 'connected',
        sessionId: sessionId,
        timestamp: new Date().toISOString(),
      }),
    );
  }

  handleDisconnect(client: WebSocket) {
    this.logger.log(`Client disconnected: ${(client as any).sessionId}`);
  }

  async processMessage(rawData: string, client: WebSocket) {
    try {
      const sessionId = (client as any).sessionId || 'unknown';
      this.logger.log(`Processing message from ${sessionId}: ${rawData}`);

      // Parse message if it's a string
      let messageData: any;
      try {
        messageData = JSON.parse(rawData);
      } catch {
        messageData = { message: rawData };
      }

      // Create input message for processing
      const inputMessage = {
        sessionId,
        message: messageData.message || messageData,
        timestamp: new Date().toISOString(),
      };

      // Process message through WebChat API service
      const response =
        await this.webchatApiService.handleWebSocketMessage(inputMessage);

      // Send response back to client
      client.send(
        JSON.stringify({
          type: 'response',
          message: response,
          timestamp: new Date().toISOString(),
        }),
      );
    } catch (error) {
      this.logger.error(`Error handling message: ${error.message}`);
      client.send(
        JSON.stringify({
          type: 'error',
          message: 'Failed to process message',
          error: error.message,
          timestamp: new Date().toISOString(),
        }),
      );
    }
  }

  // Method to send responses back to specific clients
  sendResponseToClient(sessionId: string, response: any) {
    // Find client by sessionId and send message
    this.server.clients.forEach((client: WebSocket) => {
      if (
        (client as any).sessionId === sessionId &&
        client.readyState === WebSocket.OPEN
      ) {
        client.send(JSON.stringify(response));
      }
    });
  }

  // Method to broadcast to all connected clients
  broadcast(message: any) {
    this.server.clients.forEach((client: WebSocket) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
  }
}
