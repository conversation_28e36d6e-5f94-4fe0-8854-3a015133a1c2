# WebChat Test Client

React frontend for testing WebChat functionality with WebSocket connections.

## Features

- **Real-time WebSocket communication** with webchat.api
- **Multi-environment support** (Local, Staging, Production)
- **Message history** with localStorage persistence
- **Emoji support** with shortcode conversion
- **Debug panel** for WebSocket event monitoring
- **Responsive design** with WhatsApp-like interface
- **Session management** with unique session IDs
- **Typing indicators** and connection status

## Prerequisites

This application requires the `webchat.api` to be running, as it connects to it via WebSocket for real-time messaging.

## Setup

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Start the webchat.api first:
   ```bash
   # From the workspace root
   pnpm dev:webchat.api
   ```

3. Start the development server:
   ```bash
   pnpm dev
   ```

The application will be available at `http://localhost:3001`.

## Usage

### Environment Selection
- **Local**: Connects to `ws://localhost:3002` (default webchat.api)
- **Staging**: Connects to staging WebSocket endpoint
- **Production**: Connects to production WebSocket endpoint

### Features

#### Chat Interface
- Send messages by typing and pressing Enter or clicking "Enviar"
- Messages are automatically saved to localStorage
- Emoji shortcodes are converted to emojis (e.g., `:smile:` → 😄)
- Clear chat history with the "Limpar" button

#### Debug Mode
- Toggle debug panel to see WebSocket events
- View connection logs, message data, and errors
- Useful for troubleshooting connection issues

#### Session Management
- Each browser session gets a unique session ID
- Session ID is displayed in the status bar
- Session persists across page reloads

## Architecture

```
React Frontend → WebSocket → webchat.api → Processing Logic
```

### Components

- **WebChatTest**: Main chat interface component
- **useWebSocket**: Custom hook for WebSocket management
- **Utils**: Helper functions for emojis, localStorage, etc.

### Key Features

1. **Real-time Communication**: Direct WebSocket connection to webchat.api
2. **State Management**: React hooks for local state management
3. **Persistence**: localStorage for message history and session data
4. **Error Handling**: Comprehensive error handling and user feedback
5. **Responsive Design**: Mobile-friendly interface

## Development

### Available Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build
- `pnpm lint` - Run ESLint

### Project Structure

```
src/
├── components/
│   ├── WebChatTest.tsx     # Main chat component
│   └── WebChatTest.css     # Component styles
├── hooks/
│   └── useWebSocket.ts     # WebSocket management hook
├── types/
│   └── index.ts            # TypeScript type definitions
├── utils/
│   └── index.ts            # Utility functions
├── App.tsx                 # Root component
├── App.css                 # Global styles
└── main.tsx                # Application entry point
```

## WebSocket Protocol

The client expects WebSocket messages in JSON format:

```json
{
  "type": "response|error|connection",
  "message": "Message content"
}
```

Plain text messages are also supported as fallback.

## Troubleshooting

### Connection Issues
1. Ensure webchat.api is running on the correct port
2. Check browser console for WebSocket errors
3. Enable debug mode to see detailed connection logs
4. Verify the correct environment is selected

### Message Issues
1. Check if WebSocket connection is established (status bar)
2. Verify message format in debug panel
3. Ensure webchat.api is processing messages correctly

## Browser Support

- Modern browsers with WebSocket support
- Chrome, Firefox, Safari, Edge (latest versions)
- Mobile browsers (iOS Safari, Chrome Mobile)
