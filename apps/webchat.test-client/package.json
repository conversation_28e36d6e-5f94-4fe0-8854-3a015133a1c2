{"name": "@lisa/webchat.test-client", "version": "0.0.1", "description": "WebChat Test Client - Testing interface for WebChat functionality", "private": true, "scripts": {"build": "nest build", "start": "nest start", "dev": "nest start --watch", "start:dev": "nest start --watch", "start:prod": "node dist/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@lisa/common": "workspace:*", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "ws": "^8.18.2"}, "devDependencies": {"@nestjs/cli": "^11.0.0", "@nestjs/testing": "^11.0.1", "@types/express": "^5.0.0", "@types/node": "^22.10.7", "@types/ws": "^8.5.13", "jest": "^30.0.2", "ts-jest": "^29.2.5", "typescript": "^5.8.3"}}