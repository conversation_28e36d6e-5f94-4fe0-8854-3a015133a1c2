* {
  box-sizing: border-box;
}

.webchat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #e5ddd5;
}

.webchat-header {
  display: flex;
  background-color: #075e54;
  color: white;
  padding: 16px 24px;
  font-size: 24px;
  font-weight: bold;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.status-bar {
  padding: 8px 24px;
  font-size: 14px;
  color: #ffffffcc;
  background-color: #128c7e;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  background-image: url('https://www.transparenttextures.com/patterns/paper-fibers.png');
}

.chatbox {
  min-width: 7rem;
  max-width: 50%;
  margin: 10px;
  padding: 12px 16px;
  border-radius: 12px;
}

.chatbox .username {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 5px;
}

.chatbox .message {
  line-height: 1.4;
  font-size: 16px;
  word-wrap: break-word;
}

.chatbox .timestamp {
  font-size: 12px;
  color: #8d8d8d;
  margin-top: 5px;
}

.chatbox.sent {
  align-self: flex-end;
  background-color: #dcf8c6;
  border-top-right-radius: 0;
}

.chatbox.sent .timestamp {
  text-align: end;
}

.chatbox.received {
  align-self: flex-start;
  background-color: #ffffff;
  border-top-left-radius: 0;
}

.chatbox.system {
  align-self: center;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  font-style: italic;
  max-width: 80%;
}

.input-area {
  display: flex;
  padding: 16px;
  background-color: #f0f0f0;
  border-top: 1px solid #ccc;
  gap: 12px;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  font-size: 16px;
  border: 1px solid #ccc;
  border-radius: 24px;
  outline: none;
}

.message-input:disabled {
  background-color: #f5f5f5;
  color: #999;
}

button {
  padding: 12px 24px;
  font-size: 16px;
  border: none;
  border-radius: 24px;
  background-color: #128c7e;
  color: white;
  cursor: pointer;
  white-space: nowrap;
}

button:hover:not(:disabled) {
  background-color: #075e54;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

select {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  color: #333;
}

.debug-toggle {
  background-color: #6c757d;
  font-size: 12px;
  padding: 6px 12px;
}

.debug-panel {
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
  padding: 12px 24px;
  font-size: 12px;
  color: #6c757d;
  max-height: 200px;
  overflow-y: auto;
}

.debug-content {
  margin-top: 8px;
}

.debug-entry {
  margin-bottom: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #e9ecef;
}

.debug-data {
  font-size: 10px;
  margin-left: 10px;
  color: #666;
  background-color: #f1f3f4;
  padding: 4px 8px;
  border-radius: 4px;
  margin-top: 4px;
  overflow-x: auto;
}
