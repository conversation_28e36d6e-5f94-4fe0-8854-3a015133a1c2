import { useCallback, useEffect, useRef, useState } from 'react';
import { useWebSocket } from '../hooks/useWebSocket';
import { DebugLog, Environment, Message, MessageType } from '../types';
import {
  clearChatHistory,
  formatTime,
  generateUUID,
  getUsernameForMessage,
  loadChatHistory,
  loadMessagesFromHistory,
  replaceEmojis,
  saveChatHistory,
  saveMessageToHistory,
} from '../utils';
import './WebChatTest.css';

export const WebChatTest: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [environment, setEnvironment] = useState<Environment>('local');
  const [sessionId, setSessionId] = useState('');
  const [debugMode, setDebugMode] = useState(false);
  const [debugLogs, setDebugLogs] = useState<DebugLog[]>([]);
  const [typingTimer, setTypingTimer] = useState<NodeJS.Timeout | null>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize session and load history
  useEffect(() => {
    const history = loadChatHistory();
    setEnvironment((history.environment as Environment) || 'local');
    setSessionId(history.sessionId || generateUUID());
    setMessages(loadMessagesFromHistory());
  }, []);

  // Save session info when it changes
  useEffect(() => {
    if (sessionId) {
      saveChatHistory({ environment, sessionId });
    }
  }, [environment, sessionId]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const addMessage = useCallback(
    (text: string, type: MessageType, save = true) => {
      const message: Message = {
        id: generateUUID(),
        text,
        type,
        time: new Date(),
        username: getUsernameForMessage(type),
      };

      setMessages((prev) => [...prev, message]);

      if (save) {
        saveMessageToHistory(message);
      }
    },
    [],
  );

  const handleWebSocketMessage = useCallback(
    (message: string, type: 'response' | 'error' | 'plain') => {
      if (type === 'error') {
        addMessage(message, 'system', false);
      } else {
        addMessage(message, 'received');
      }
    },
    [addMessage],
  );

  const handleDebugLog = useCallback((log: DebugLog) => {
    setDebugLogs((prev) => [...prev, log]);
  }, []);

  const { status, sendMessage, isConnected } = useWebSocket({
    environment,
    sessionId,
    onMessage: handleWebSocketMessage,
    onDebugLog: debugMode ? handleDebugLog : undefined,
  });

  const handleSendMessage = useCallback(() => {
    const message = inputValue.trim();
    if (!message || !isConnected) return;

    const success = sendMessage(message);
    if (success) {
      addMessage(message, 'sent');
      setInputValue('');
    }
  }, [inputValue, isConnected, sendMessage, addMessage]);

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter') {
        handleSendMessage();
      }
    },
    [handleSendMessage],
  );

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(e.target.value);

      // Typing indicator logic
      if (typingTimer) {
        clearTimeout(typingTimer);
      }

      if (isConnected && e.target.value) {
        const timer = setTimeout(() => {
          // Could send typing stopped event here
        }, 1000);
        setTypingTimer(timer);
      }
    },
    [isConnected, typingTimer],
  );

  const handleEnvironmentChange = useCallback(
    (e: React.ChangeEvent<HTMLSelectElement>) => {
      const newEnv = e.target.value as Environment;
      setEnvironment(newEnv);
      addMessage(`Mudando para ambiente: ${newEnv}`, 'system', false);
    },
    [addMessage],
  );

  const handleClearChat = useCallback(() => {
    setMessages([]);
    clearChatHistory();
    addMessage('Chat limpo', 'system', false);
  }, [addMessage]);

  const toggleDebug = useCallback(() => {
    setDebugMode((prev) => !prev);
    if (!debugMode) {
      setDebugLogs([]);
    }
  }, [debugMode]);

  const getStatusText = () => {
    switch (status) {
      case 'connecting':
        return 'Conectando...';
      case 'connected':
        return 'Online';
      case 'disconnected':
        return 'Desconectado';
      case 'error':
        return 'Erro na conexão';
      case 'typing':
        return 'Digitando...';
      default:
        return 'Desconhecido';
    }
  };

  return (
    <div className="webchat-container">
      <header className="webchat-header">
        <div>Lisa WebChat Test Client</div>
        <div className="header-controls">
          <select value={environment} onChange={handleEnvironmentChange}>
            <option value="local">Local</option>
            <option value="stg">Staging</option>
            <option value="prod">Production</option>
          </select>
          <button className="debug-toggle" onClick={toggleDebug}>
            Debug
          </button>
        </div>
      </header>

      <div className="status-bar">
        <span>{getStatusText()}</span>
        <div className="status-info">
          <span>Sessão: {sessionId.substring(0, 8)}...</span>
          <span>Ambiente: {environment}</span>
        </div>
      </div>

      <div className="chat-area">
        {messages.map((message) => (
          <div key={message.id} className={`chatbox ${message.type}`}>
            <div className="username">{message.username}</div>
            <div className="message">{replaceEmojis(message.text)}</div>
            <div className="timestamp">{formatTime(message.time)}</div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      <div className="input-area">
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder="Digite sua mensagem..."
          disabled={!isConnected}
          className="message-input"
        />
        <button onClick={handleSendMessage} disabled={!isConnected}>
          Enviar
        </button>
        <button onClick={handleClearChat}>Limpar</button>
      </div>

      {debugMode && (
        <div className="debug-panel">
          <strong>Debug Info:</strong>
          <div className="debug-content">
            {debugLogs.length === 0 ? (
              <div>WebSocket events will appear here...</div>
            ) : (
              debugLogs.map((log) => (
                <div key={log.id} className="debug-entry">
                  <strong>[{formatTime(log.timestamp)}]</strong> {log.message}
                  {log.data && (
                    <pre className="debug-data">
                      {JSON.stringify(log.data, null, 2)}
                    </pre>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
};
