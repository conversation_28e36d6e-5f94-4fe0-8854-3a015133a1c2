import { useCallback, useEffect, useRef, useState } from 'react';
import {
  ConnectionStatus,
  DebugLog,
  Environment,
  WebSocketMessage,
} from '../types';

interface UseWebSocketProps {
  environment: Environment;
  sessionId: string;
  onMessage: (message: string, type: 'response' | 'error' | 'plain') => void;
  onDebugLog?: (log: DebugLog) => void;
}

const ENDPOINTS: Record<Environment, string> = {
  local: 'ws://localhost:3002',
  stg: 'wss://lisa-webchat-stg.azurewebsites.net',
  prod: 'wss://lisa-webchat-prod.azurewebsites.net',
};

export const useWebSocket = ({
  environment,
  sessionId,
  onMessage,
  onDebugLog,
}: UseWebSocketProps) => {
  const [status, setStatus] = useState<ConnectionStatus>('disconnected');
  const socketRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();

  const addDebugLog = useCallback(
    (message: string, data?: Record<string, any> | string | null) => {
      if (onDebugLog) {
        onDebugLog({
          id: crypto.randomUUID(),
          timestamp: new Date(),
          message,
          data,
        });
      }
    },
    [onDebugLog],
  );

  const connect = useCallback(() => {
    const endpoint = ENDPOINTS[environment];
    const url = `${endpoint}?sessionId=${sessionId}`;

    addDebugLog(`Connecting to: ${url}`);
    setStatus('connecting');

    // Close existing connection
    if (socketRef.current) {
      socketRef.current.close();
    }

    const socket = new WebSocket(url);
    socketRef.current = socket;

    socket.onopen = () => {
      setStatus('connected');
      addDebugLog('WebSocket connected successfully');
      onMessage('Conectado ao Lisa WebChat! 🚀', 'response');
    };

    socket.onmessage = (event) => {
      addDebugLog('Message received', { data: event.data });

      try {
        const data: WebSocketMessage = JSON.parse(event.data);

        if (data.type === 'response') {
          onMessage(data.message, 'response');
        } else if (data.type === 'connection') {
          addDebugLog('Connection confirmed', data);
        } else if (data.type === 'error') {
          onMessage(`Erro: ${data.message}`, 'error');
        } else {
          // Fallback for plain text messages
          onMessage(data.message || event.data, 'response');
        }
      } catch {
        // Handle plain text messages
        onMessage(event.data, 'plain');
      }
    };

    socket.onclose = (event) => {
      setStatus('disconnected');
      addDebugLog('WebSocket disconnected', {
        code: event.code,
        reason: event.reason,
      });
      onMessage('Desconectado do servidor', 'error');
    };

    socket.onerror = () => {
      setStatus('error');
      addDebugLog('WebSocket error occurred');
      onMessage('Erro na conexão com o servidor', 'error');
    };
  }, [environment, sessionId, onMessage, addDebugLog]);

  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
  }, []);

  const sendMessage = useCallback(
    (message: string) => {
      if (socketRef.current?.readyState === WebSocket.OPEN) {
        addDebugLog('Sending message', { message });
        socketRef.current.send(JSON.stringify({ message }));
        return true;
      }
      return false;
    },
    [addDebugLog],
  );

  const reconnect = useCallback(() => {
    disconnect();
    reconnectTimeoutRef.current = setTimeout(() => {
      connect();
    }, 1000);
  }, [connect, disconnect]);

  // Auto-connect on mount and environment change
  useEffect(() => {
    connect();
    return () => disconnect();
  }, [connect, disconnect]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    status,
    sendMessage,
    reconnect,
    disconnect,
    isConnected: status === 'connected',
  };
};
