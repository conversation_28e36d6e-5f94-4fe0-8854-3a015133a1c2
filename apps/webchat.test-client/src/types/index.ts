export type MessageType = 'sent' | 'received' | 'system';

export interface Message {
  id: string;
  text: string;
  type: MessageType;
  time: Date;
  username?: string;
}

export type Environment = 'local' | 'stg' | 'prod';

export interface WebSocketMessage {
  type?: string;
  message: string;
  data?: unknown;
}

export interface ChatHistory {
  messages: Array<{
    text: string;
    type: MessageType;
    time: string;
  }>;
  environment?: Environment;
  sessionId?: string;
}

export type ConnectionStatus =
  | 'connecting'
  | 'connected'
  | 'disconnected'
  | 'error'
  | 'typing';

export interface DebugLog {
  id: string;
  timestamp: Date;
  message: string;
  data?: Record<string, any> | string | null;
}
