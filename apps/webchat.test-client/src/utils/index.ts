import { ChatHistory, Message, MessageType } from '../types';

// Emoji mapping
const EMOJI_MAP: Record<string, string> = {
  ':smile:': '😄',
  ':heart:': '❤️',
  ':thumbsup:': '👍',
  ':laugh:': '😂',
  ':sad:': '😢',
  ':fire:': '🔥',
  ':rocket:': '🚀',
  ':coffee:': '☕',
  ':pizza:': '🍕',
};

export const replaceEmojis = (text: string): string => {
  let result = text;
  for (const [code, emoji] of Object.entries(EMOJI_MAP)) {
    result = result.replace(
      new RegExp(code.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'),
      emoji,
    );
  }
  return result;
};

export const generateUUID = (): string => {
  return crypto.randomUUID();
};

export const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('pt-BR', {
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const getUsernameForMessage = (type: MessageType): string => {
  switch (type) {
    case 'sent':
      return 'Você';
    case 'received':
      return 'Lisa';
    case 'system':
      return 'Sistema';
    default:
      return 'Desconhecido';
  }
};

// Local Storage utilities
const STORAGE_KEY = 'lisaWebChatHistory';

export const loadChatHistory = (): ChatHistory => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    return stored ? JSON.parse(stored) : { messages: [] };
  } catch {
    return { messages: [] };
  }
};

export const saveChatHistory = (history: Partial<ChatHistory>): void => {
  try {
    const current = loadChatHistory();
    const updated = { ...current, ...history };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
  } catch {
    // Silently fail if localStorage is not available
  }
};

export const saveMessageToHistory = (message: Message): void => {
  if (message.type === 'system') return; // Don't save system messages

  const history = loadChatHistory();
  const messages = history.messages || [];
  messages.push({
    text: message.text,
    type: message.type,
    time: message.time.toISOString(),
  });
  saveChatHistory({ messages });
};

export const clearChatHistory = (): void => {
  saveChatHistory({ messages: [] });
};

export const loadMessagesFromHistory = (): Message[] => {
  const history = loadChatHistory();
  return (history.messages || []).map(({ text, type, time }) => ({
    id: generateUUID(),
    text,
    type,
    time: new Date(time),
    username: getUsernameForMessage(type),
  }));
};
