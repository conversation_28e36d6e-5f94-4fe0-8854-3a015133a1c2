# 🛡️ Sistema de Validação de Tags - Projeto Lisa

Sistema completo de validação de tags para prevenir erros de deploy e garantir que apenas tags válidas disparem pipelines.

## 🎯 Problemas Resolvidos

### ❌ **Antes (Problemas)**

- Tags com nomes incorretos não disparavam pipeline (sem feedback)
- Tags com versões erradas falhavam apenas no build
- Possibilidade de criar tags para projetos inexistentes
- Falta de validação local antes do push

### ✅ **Depois (Soluções)**

- **Pipeline de fallback** para tags não reconhecidas
- **Validação local** antes do push (git hook)
- **Script helper** para criar tags com validação
- **Feedback claro** sobre erros e como corrigi-los

## 🏗️ Componentes do Sistema

### 1. **Pipeline de Fallback** (`bitbucket-pipelines.yml`)

```yaml
# Captura tags não reconhecidas
'@lisa/*':
  - step:
      name: <PERSON><PERSON><PERSON> Unknown Tag
      script:
        - ./scripts/validate-unknown-tag.sh $BITBUCKET_TAG
```

**Cenários capturados:**

- `@lisa/projeto-inexistente@1.0.0` ❌
- `@lisa/ai.api@versao-errada` ❌
- `@lisa/novo-projeto@1.0.0` ⚠️ (projeto válido mas não configurado)

### 2. **Git Hook Pre-Push** (`.husky/pre-push`)

Valida tags **antes** do push para o repositório:

```bash
# Executa automaticamente ao fazer:
git push origin @lisa/ai.api@1.0.0
```

**Validações realizadas:**

- ✅ Projeto existe?
- ✅ Package.json existe?
- ✅ Tag corresponde ao package.json?
- ⚠️ Formato da tag está correto?

### 3. **Script de Criação de Tags** (`scripts/create-tag.sh`)

Helper para criar tags com validação completa:

```bash
# Uso básico
./scripts/create-tag.sh ai.api              # Usa versão do package.json
./scripts/create-tag.sh ai.api 1.0.0        # Usa versão específica

# Via npm scripts
pnpm create-tag:ai.api
pnpm create-tag:twilio.api
pnpm create-tag:smarters.api
```

### 4. **Validador de Tags Desconhecidas** (`scripts/validate-unknown-tag.sh`)

Analisa tags que não correspondem a projetos configurados:

- 🔍 Extrai nome do projeto da tag
- 📁 Verifica se diretório existe
- 📄 Verifica se tem package.json
- 🐳 Verifica se tem Dockerfile
- 💡 Fornece instruções para adicionar ao pipeline

## 🚀 Fluxos de Uso

### **Fluxo Normal (Recomendado)**

```bash
# 1. Atualizar versão no package.json
cd apps/ai.api
# Editar: "version": "1.0.0"

# 2. Commit da nova versão
git add package.json
git commit -m "chore: bump ai.api to 1.0.0"
git push origin main

# 3. Criar tag com validação
pnpm create-tag:ai.api

# 4. Confirmar e fazer push
# Script pergunta se quer fazer push automaticamente
```

### **Fluxo Manual**

```bash
# 1. Criar tag manualmente
git tag @lisa/ai.api@1.0.0

# 2. Push (validação automática via hook)
git push origin @lisa/ai.api@1.0.0
```

### **Fluxo de Teste**

```bash
# Testar validação antes de criar tag
pnpm test:tag-validation:ai.api

# Testar cenários de erro
pnpm test:tag-validation:error
```

## ⚠️ Cenários de Erro e Soluções

### **1. Tag para Projeto Inexistente**

```bash
git tag @lisa/projeto-nao-existe@1.0.0
git push origin @lisa/projeto-nao-existe@1.0.0
```

**Resultado:**

- ❌ Pipeline de fallback é disparada
- 📋 Lista projetos disponíveis
- 💡 Sugere correção da tag

### **2. Tag com Versão Incorreta**

```bash
# package.json tem version: "0.0.1"
git tag @lisa/ai.api@1.0.0  # Versão diferente
git push origin @lisa/ai.api@1.0.0
```

**Resultado:**

- ❌ Git hook bloqueia o push
- 💡 Sugere atualizar package.json ou corrigir tag

### **3. Projeto Válido Não Configurado**

```bash
# Projeto existe mas não está no pipeline
git tag @lisa/novo-projeto@1.0.0
git push origin @lisa/novo-projeto@1.0.0
```

**Resultado:**

- ⚠️ Pipeline de fallback detecta projeto válido
- 📝 Fornece código para adicionar ao pipeline
- 🔧 Sugere próximos passos

## 🧪 Comandos de Teste

```bash
# Testar todos os projetos
pnpm test:tag-validation

# Testar apenas erros (demonstração)
pnpm test:tag-validation:error

# Testar projeto específico
pnpm test:tag-validation:ai.api
pnpm test:tag-validation:twilio.api
pnpm test:tag-validation:smarters.api

# Criar tags com validação
pnpm create-tag:ai.api
pnpm create-tag:twilio.api
pnpm create-tag:smarters.api
```

## 📊 Benefícios do Sistema

### **Para Desenvolvedores**

- ✅ Feedback imediato sobre erros
- 🛡️ Prevenção de tags incorretas
- 🎯 Processo guiado para criar tags
- 📚 Documentação clara de erros

### **Para CI/CD**

- 🚫 Evita builds desnecessários
- 📋 Log claro de tags não reconhecidas
- 🔄 Pipeline de fallback para casos edge
- 📈 Melhor observabilidade

### **Para o Projeto**

- 🎯 Padronização de tags
- 📝 Documentação automática de erros
- 🔧 Facilita adição de novos projetos
- 🛠️ Manutenção simplificada

## 🔧 Configuração para Novos Projetos

Para adicionar um novo projeto ao sistema:

### 1. **Adicionar ao Pipeline**

```yaml
# bitbucket-pipelines.yml
'@lisa/novo-projeto@*':
  - step:
      name: Build and Push Novo Projeto Image
      script:
        - ./scripts/docker-build.sh $BITBUCKET_TAG apps/novo-projeto push
      services:
        - docker
```

### 2. **Adicionar Scripts NPM**

```json
// package.json
{
  "scripts": {
    "test:tag-validation:novo-projeto": "bash scripts/test-tag-validation.sh \"\" novo-projeto",
    "create-tag:novo-projeto": "bash scripts/create-tag.sh novo-projeto"
  }
}
```

### 3. **Atualizar Scripts**

```bash
# scripts/test-tag-validation.sh
PROJECTS=("ai.api" "twilio.api" "smarters.api" "novo-projeto")
```

## 📈 Próximos Passos

- [ ] Integrar com sistema de versionamento automático
- [ ] Adicionar notificações de Slack para tags inválidas
- [ ] Implementar rollback automático em caso de falha
- [ ] Criar dashboard de tags e deploys
