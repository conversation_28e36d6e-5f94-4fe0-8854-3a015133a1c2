{"name": "@lisa/common", "version": "0.0.1", "description": "Common DTOs and shared types", "private": true, "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}}, "scripts": {"build": "tsup", "build:tsc": "tsc -p tsconfig.lib.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"zod": "^3.25.41"}, "devDependencies": {"@types/node": "^22.10.7", "jest": "^30.0.2", "ts-jest": "^29.2.5", "tsup": "^8.5.0", "typescript": "^5.8.3"}}