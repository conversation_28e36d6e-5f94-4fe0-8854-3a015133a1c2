{"name": "@lisa/crm-sdk", "version": "0.0.1", "description": "CRM SDK integrations", "private": true, "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./client": {"types": "./dist/client.d.ts", "import": "./dist/client.js", "require": "./dist/client.cjs"}, "./types": {"types": "./dist/types.d.ts", "import": "./dist/types.js", "require": "./dist/types.cjs"}, "./models": {"types": "./dist/models.d.ts", "import": "./dist/models.js", "require": "./dist/models.cjs"}, "./schemas": {"types": "./dist/schemas.d.ts", "import": "./dist/schemas.js", "require": "./dist/schemas.cjs"}}, "scripts": {"build": "tsup", "build:tsc": "tsc -p tsconfig.lib.json", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage"}, "dependencies": {"@lisa/twenty-sdk": "workspace:*", "axios": "^1.9.0", "graphql-request": "^7.2.0", "uuid": "^11.1.0", "zod": "^3.25.41"}, "devDependencies": {"@types/node": "^22.10.7", "@types/uuid": "^10.0.0", "jest": "^30.0.2", "ts-jest": "^29.2.5", "tsup": "^8.5.0", "typescript": "^5.8.3"}}