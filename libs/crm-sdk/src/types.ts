// Re-export types from twenty-sdk to maintain abstraction layer
import { Homer<PERSON><PERSON> } from '@lisa/twenty-sdk';

// Company types
export type CompanyForResponse = HomerApi.CompanyForResponse;

// Property types
export type Property = HomerApi.Property;
export type PropertyForResponse = HomerApi.PropertyForResponse;
export type PropertyForUpdate = HomerApi.PropertyForUpdate;

// Person types
export type PersonForResponse = HomerApi.PersonForResponse;

// Attachment types
export type TwentyAttachment = HomerApi.Attachment;
export type AttachmentForResponse = HomerApi.AttachmentForResponse;

// Property enums - export both values and types
export const PropertyType = HomerApi.Property.PropertyType;
export const ConstructionStatus = HomerApi.Property.ConstructionStatus;
export const IntegrationType = HomerApi.Property.IntegrationType;
export const ListingType = HomerApi.Property.ListingType;

// Export enum types for TypeScript
export type PropertyTypeEnum = HomerApi.Property.PropertyType;
export type ConstructionStatusEnum = HomerApi.Property.ConstructionStatus;
export type IntegrationTypeEnum = HomerApi.Property.IntegrationType;
export type ListingTypeEnum = HomerApi.Property.ListingType;
