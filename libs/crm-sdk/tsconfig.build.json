{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "Node", "declaration": true, "declarationMap": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "noEmit": false, "resolveJsonModule": true, "verbatimModuleSyntax": false, "baseUrl": ".", "paths": {"@lisa/twenty-sdk": ["../../libs/twenty-sdk/src"], "@lisa/twenty-sdk/*": ["../../libs/twenty-sdk/src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}