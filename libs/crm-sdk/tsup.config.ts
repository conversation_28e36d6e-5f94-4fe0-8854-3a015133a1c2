import { defineConfig } from 'tsup';

export default defineConfig({
  entry: {
    index: 'src/index.ts',
    client: 'src/client.ts',
    types: 'src/types.ts',
    models: 'src/models.ts',
    schemas: 'src/schemas.ts',
  },
  format: ['esm', 'cjs'],
  dts: true,
  splitting: false,
  sourcemap: true,
  clean: true,
  outDir: 'dist',
  target: 'es2022',
  minify: false,
  bundle: false,
  external: ['@lisa/twenty-sdk', 'axios', 'graphql-request', 'uuid', 'zod'],
  tsconfig: './tsconfig.build.json',
});
