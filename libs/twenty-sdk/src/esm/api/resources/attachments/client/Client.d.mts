/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace Attachments {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Attachments`
 */
export declare class Attachments {
    protected readonly _options: Attachments.Options;
    constructor(_options: Attachments.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **attachments**
     *
     * @param {HomerApi.FindManyAttachmentsRequest} request
     * @param {Attachments.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.attachments.findManyAttachments()
     */
    findManyAttachments(request?: HomerApi.FindManyAttachmentsRequest, requestOptions?: Attachments.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyAttachmentsResponse>;
    private __findManyAttachments;
    /**
     * @param {HomerApi.CreateOneAttachmentRequest} request
     * @param {Attachments.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.attachments.createOneAttachment({
     *         body: {
     *             authorId: "authorId"
     *         }
     *     })
     */
    createOneAttachment(request: HomerApi.CreateOneAttachmentRequest, requestOptions?: Attachments.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneAttachmentResponse>;
    private __createOneAttachment;
    /**
     * @param {HomerApi.CreateManyAttachmentsRequest} request
     * @param {Attachments.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.attachments.createManyAttachments({
     *         body: [{
     *                 authorId: "authorId"
     *             }]
     *     })
     */
    createManyAttachments(request: HomerApi.CreateManyAttachmentsRequest, requestOptions?: Attachments.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyAttachmentsResponse>;
    private __createManyAttachments;
    /**
     * **depth** can be provided to request your **attachment**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneAttachmentRequest} request
     * @param {Attachments.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.attachments.findOneAttachment("id")
     */
    findOneAttachment(id: string, request?: HomerApi.FindOneAttachmentRequest, requestOptions?: Attachments.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneAttachmentResponse>;
    private __findOneAttachment;
    /**
     * @param {string} id - Object id.
     * @param {Attachments.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.attachments.deleteOneAttachment("id")
     */
    deleteOneAttachment(id: string, requestOptions?: Attachments.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneAttachmentResponse>;
    private __deleteOneAttachment;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.AttachmentForUpdate} request
     * @param {Attachments.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.attachments.updateOneAttachment("id")
     */
    updateOneAttachment(id: string, request?: HomerApi.AttachmentForUpdate, requestOptions?: Attachments.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneAttachmentResponse>;
    private __updateOneAttachment;
    /**
     * **depth** can be provided to request your **attachment**
     *
     * @param {HomerApi.FindAttachmentDuplicatesRequest} request
     * @param {Attachments.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.attachments.findAttachmentDuplicates()
     */
    findAttachmentDuplicates(request?: HomerApi.FindAttachmentDuplicatesRequest, requestOptions?: Attachments.RequestOptions): core.HttpResponsePromise<HomerApi.FindAttachmentDuplicatesResponse>;
    private __findAttachmentDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
