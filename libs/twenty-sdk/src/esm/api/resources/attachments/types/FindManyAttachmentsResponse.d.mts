/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyAttachmentsResponse {
    data?: FindManyAttachmentsResponse.Data;
    pageInfo?: FindManyAttachmentsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyAttachmentsResponse {
    interface Data {
        attachments?: HomerApi.AttachmentForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
