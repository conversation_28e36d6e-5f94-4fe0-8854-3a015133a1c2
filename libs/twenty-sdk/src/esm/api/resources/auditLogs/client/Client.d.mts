/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace AuditLogs {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `AuditLogs`
 */
export declare class AuditLogs {
    protected readonly _options: AuditLogs.Options;
    constructor(_options: AuditLogs.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **auditLogs**
     *
     * @param {HomerApi.FindManyAuditLogsRequest} request
     * @param {AuditLogs.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.auditLogs.findManyAuditLogs()
     */
    findManyAuditLogs(request?: HomerApi.FindManyAuditLogsRequest, requestOptions?: AuditLogs.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyAuditLogsResponse>;
    private __findManyAuditLogs;
    /**
     * @param {HomerApi.CreateOneAuditLogRequest} request
     * @param {AuditLogs.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.auditLogs.createOneAuditLog({
     *         body: {}
     *     })
     */
    createOneAuditLog(request: HomerApi.CreateOneAuditLogRequest, requestOptions?: AuditLogs.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneAuditLogResponse>;
    private __createOneAuditLog;
    /**
     * @param {HomerApi.CreateManyAuditLogsRequest} request
     * @param {AuditLogs.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.auditLogs.createManyAuditLogs({
     *         body: [{}]
     *     })
     */
    createManyAuditLogs(request: HomerApi.CreateManyAuditLogsRequest, requestOptions?: AuditLogs.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyAuditLogsResponse>;
    private __createManyAuditLogs;
    /**
     * **depth** can be provided to request your **auditLog**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneAuditLogRequest} request
     * @param {AuditLogs.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.auditLogs.findOneAuditLog("id")
     */
    findOneAuditLog(id: string, request?: HomerApi.FindOneAuditLogRequest, requestOptions?: AuditLogs.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneAuditLogResponse>;
    private __findOneAuditLog;
    /**
     * @param {string} id - Object id.
     * @param {AuditLogs.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.auditLogs.deleteOneAuditLog("id")
     */
    deleteOneAuditLog(id: string, requestOptions?: AuditLogs.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneAuditLogResponse>;
    private __deleteOneAuditLog;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.AuditLogForUpdate} request
     * @param {AuditLogs.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.auditLogs.updateOneAuditLog("id")
     */
    updateOneAuditLog(id: string, request?: HomerApi.AuditLogForUpdate, requestOptions?: AuditLogs.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneAuditLogResponse>;
    private __updateOneAuditLog;
    /**
     * **depth** can be provided to request your **auditLog**
     *
     * @param {HomerApi.FindAuditLogDuplicatesRequest} request
     * @param {AuditLogs.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.auditLogs.findAuditLogDuplicates()
     */
    findAuditLogDuplicates(request?: HomerApi.FindAuditLogDuplicatesRequest, requestOptions?: AuditLogs.RequestOptions): core.HttpResponsePromise<HomerApi.FindAuditLogDuplicatesResponse>;
    private __findAuditLogDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
