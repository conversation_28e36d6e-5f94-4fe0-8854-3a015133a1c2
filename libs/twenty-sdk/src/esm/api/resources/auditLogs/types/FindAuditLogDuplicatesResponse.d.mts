/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindAuditLogDuplicatesResponse {
    data?: FindAuditLogDuplicatesResponse.Data.Item[];
}
export declare namespace FindAuditLogDuplicatesResponse {
    type Data = Data.Item[];
    namespace Data {
        interface Item {
            totalCount?: number;
            pageInfo?: Item.PageInfo;
            companyDuplicates?: HomerApi.AuditLogForResponse[];
        }
        namespace Item {
            interface PageInfo {
                hasNextPage?: boolean;
                startCursor?: string;
                endCursor?: string;
            }
        }
    }
}
