/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyAuditLogsResponse {
    data?: FindManyAuditLogsResponse.Data;
    pageInfo?: FindManyAuditLogsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyAuditLogsResponse {
    interface Data {
        auditLogs?: HomerApi.AuditLogForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
