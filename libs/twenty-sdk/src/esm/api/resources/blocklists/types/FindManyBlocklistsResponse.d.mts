/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyBlocklistsResponse {
    data?: FindManyBlocklistsResponse.Data;
    pageInfo?: FindManyBlocklistsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyBlocklistsResponse {
    interface Data {
        blocklists?: HomerApi.BlocklistForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
