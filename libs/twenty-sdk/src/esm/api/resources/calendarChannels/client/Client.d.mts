/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace CalendarChannels {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `CalendarChannels`
 */
export declare class CalendarChannels {
    protected readonly _options: CalendarChannels.Options;
    constructor(_options: CalendarChannels.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **calendarChannels**
     *
     * @param {HomerApi.FindManyCalendarChannelsRequest} request
     * @param {CalendarChannels.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarChannels.findManyCalendarChannels()
     */
    findManyCalendarChannels(request?: HomerApi.FindManyCalendarChannelsRequest, requestOptions?: CalendarChannels.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyCalendarChannelsResponse>;
    private __findManyCalendarChannels;
    /**
     * @param {HomerApi.CreateOneCalendarChannelRequest} request
     * @param {CalendarChannels.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarChannels.createOneCalendarChannel({
     *         body: {
     *             connectedAccountId: "connectedAccountId"
     *         }
     *     })
     */
    createOneCalendarChannel(request: HomerApi.CreateOneCalendarChannelRequest, requestOptions?: CalendarChannels.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneCalendarChannelResponse>;
    private __createOneCalendarChannel;
    /**
     * @param {HomerApi.CreateManyCalendarChannelsRequest} request
     * @param {CalendarChannels.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarChannels.createManyCalendarChannels({
     *         body: [{
     *                 connectedAccountId: "connectedAccountId"
     *             }]
     *     })
     */
    createManyCalendarChannels(request: HomerApi.CreateManyCalendarChannelsRequest, requestOptions?: CalendarChannels.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyCalendarChannelsResponse>;
    private __createManyCalendarChannels;
    /**
     * **depth** can be provided to request your **calendarChannel**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneCalendarChannelRequest} request
     * @param {CalendarChannels.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarChannels.findOneCalendarChannel("id")
     */
    findOneCalendarChannel(id: string, request?: HomerApi.FindOneCalendarChannelRequest, requestOptions?: CalendarChannels.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneCalendarChannelResponse>;
    private __findOneCalendarChannel;
    /**
     * @param {string} id - Object id.
     * @param {CalendarChannels.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarChannels.deleteOneCalendarChannel("id")
     */
    deleteOneCalendarChannel(id: string, requestOptions?: CalendarChannels.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneCalendarChannelResponse>;
    private __deleteOneCalendarChannel;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.CalendarChannelForUpdate} request
     * @param {CalendarChannels.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarChannels.updateOneCalendarChannel("id")
     */
    updateOneCalendarChannel(id: string, request?: HomerApi.CalendarChannelForUpdate, requestOptions?: CalendarChannels.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneCalendarChannelResponse>;
    private __updateOneCalendarChannel;
    /**
     * **depth** can be provided to request your **calendarChannel**
     *
     * @param {HomerApi.FindCalendarChannelDuplicatesRequest} request
     * @param {CalendarChannels.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarChannels.findCalendarChannelDuplicates()
     */
    findCalendarChannelDuplicates(request?: HomerApi.FindCalendarChannelDuplicatesRequest, requestOptions?: CalendarChannels.RequestOptions): core.HttpResponsePromise<HomerApi.FindCalendarChannelDuplicatesResponse>;
    private __findCalendarChannelDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
