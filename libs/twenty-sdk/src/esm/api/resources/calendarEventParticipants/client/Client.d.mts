/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON>pi from "../../../index.mjs";
export declare namespace CalendarEventParticipants {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `CalendarEventParticipants`
 */
export declare class CalendarEventParticipants {
    protected readonly _options: CalendarEventParticipants.Options;
    constructor(_options: CalendarEventParticipants.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **calendarEventParticipants**
     *
     * @param {HomerApi.FindManyCalendarEventParticipantsRequest} request
     * @param {CalendarEventParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarEventParticipants.findManyCalendarEventParticipants()
     */
    findManyCalendarEventParticipants(request?: HomerApi.FindManyCalendarEventParticipantsRequest, requestOptions?: CalendarEventParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyCalendarEventParticipantsResponse>;
    private __findManyCalendarEventParticipants;
    /**
     * @param {HomerApi.CreateOneCalendarEventParticipantRequest} request
     * @param {CalendarEventParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarEventParticipants.createOneCalendarEventParticipant({
     *         body: {
     *             calendarEventId: "calendarEventId"
     *         }
     *     })
     */
    createOneCalendarEventParticipant(request: HomerApi.CreateOneCalendarEventParticipantRequest, requestOptions?: CalendarEventParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneCalendarEventParticipantResponse>;
    private __createOneCalendarEventParticipant;
    /**
     * @param {HomerApi.CreateManyCalendarEventParticipantsRequest} request
     * @param {CalendarEventParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarEventParticipants.createManyCalendarEventParticipants({
     *         body: [{
     *                 calendarEventId: "calendarEventId"
     *             }]
     *     })
     */
    createManyCalendarEventParticipants(request: HomerApi.CreateManyCalendarEventParticipantsRequest, requestOptions?: CalendarEventParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyCalendarEventParticipantsResponse>;
    private __createManyCalendarEventParticipants;
    /**
     * **depth** can be provided to request your **calendarEventParticipant**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneCalendarEventParticipantRequest} request
     * @param {CalendarEventParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarEventParticipants.findOneCalendarEventParticipant("id")
     */
    findOneCalendarEventParticipant(id: string, request?: HomerApi.FindOneCalendarEventParticipantRequest, requestOptions?: CalendarEventParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneCalendarEventParticipantResponse>;
    private __findOneCalendarEventParticipant;
    /**
     * @param {string} id - Object id.
     * @param {CalendarEventParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarEventParticipants.deleteOneCalendarEventParticipant("id")
     */
    deleteOneCalendarEventParticipant(id: string, requestOptions?: CalendarEventParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneCalendarEventParticipantResponse>;
    private __deleteOneCalendarEventParticipant;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.CalendarEventParticipantForUpdate} request
     * @param {CalendarEventParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarEventParticipants.updateOneCalendarEventParticipant("id")
     */
    updateOneCalendarEventParticipant(id: string, request?: HomerApi.CalendarEventParticipantForUpdate, requestOptions?: CalendarEventParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneCalendarEventParticipantResponse>;
    private __updateOneCalendarEventParticipant;
    /**
     * **depth** can be provided to request your **calendarEventParticipant**
     *
     * @param {HomerApi.FindCalendarEventParticipantDuplicatesRequest} request
     * @param {CalendarEventParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.calendarEventParticipants.findCalendarEventParticipantDuplicates()
     */
    findCalendarEventParticipantDuplicates(request?: HomerApi.FindCalendarEventParticipantDuplicatesRequest, requestOptions?: CalendarEventParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.FindCalendarEventParticipantDuplicatesResponse>;
    private __findCalendarEventParticipantDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
