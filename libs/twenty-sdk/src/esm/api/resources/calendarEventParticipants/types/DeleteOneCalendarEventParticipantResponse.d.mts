/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface DeleteOneCalendarEventParticipantResponse {
    data?: DeleteOneCalendarEventParticipantResponse.Data;
}
export declare namespace DeleteOneCalendarEventParticipantResponse {
    interface Data {
        deleteCalendarEventParticipant?: Data.DeleteCalendarEventParticipant;
    }
    namespace Data {
        interface DeleteCalendarEventParticipant {
            id?: string;
        }
    }
}
