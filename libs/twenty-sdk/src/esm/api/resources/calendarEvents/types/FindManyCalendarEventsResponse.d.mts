/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyCalendarEventsResponse {
    data?: FindManyCalendarEventsResponse.Data;
    pageInfo?: FindManyCalendarEventsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyCalendarEventsResponse {
    interface Data {
        calendarEvents?: HomerApi.CalendarEventForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
