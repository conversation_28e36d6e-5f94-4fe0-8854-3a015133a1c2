/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as HomerApi from "../../../index.mjs";
export declare namespace Companies {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Companies`
 */
export declare class Companies {
    protected readonly _options: Companies.Options;
    constructor(_options: Companies.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **companies**
     *
     * @param {HomerApi.FindManyCompaniesRequest} request
     * @param {Companies.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.companies.findManyCompanies()
     */
    findManyCompanies(request?: HomerApi.FindManyCompaniesRequest, requestOptions?: Companies.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyCompaniesResponse>;
    private __findManyCompanies;
    /**
     * @param {HomerApi.CreateOneCompanyRequest} request
     * @param {Companies.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.companies.createOneCompany({
     *         body: {}
     *     })
     */
    createOneCompany(request: HomerApi.CreateOneCompanyRequest, requestOptions?: Companies.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneCompanyResponse>;
    private __createOneCompany;
    /**
     * @param {HomerApi.CreateManyCompaniesRequest} request
     * @param {Companies.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.companies.createManyCompanies({
     *         body: [{}]
     *     })
     */
    createManyCompanies(request: HomerApi.CreateManyCompaniesRequest, requestOptions?: Companies.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyCompaniesResponse>;
    private __createManyCompanies;
    /**
     * **depth** can be provided to request your **company**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneCompanyRequest} request
     * @param {Companies.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.companies.findOneCompany("id")
     */
    findOneCompany(id: string, request?: HomerApi.FindOneCompanyRequest, requestOptions?: Companies.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneCompanyResponse>;
    private __findOneCompany;
    /**
     * @param {string} id - Object id.
     * @param {Companies.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.companies.deleteOneCompany("id")
     */
    deleteOneCompany(id: string, requestOptions?: Companies.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneCompanyResponse>;
    private __deleteOneCompany;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.CompanyForUpdate} request
     * @param {Companies.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.companies.updateOneCompany("id")
     */
    updateOneCompany(id: string, request?: HomerApi.CompanyForUpdate, requestOptions?: Companies.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneCompanyResponse>;
    private __updateOneCompany;
    /**
     * **depth** can be provided to request your **company**
     *
     * @param {HomerApi.FindCompanyDuplicatesRequest} request
     * @param {Companies.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.companies.findCompanyDuplicates()
     */
    findCompanyDuplicates(request?: HomerApi.FindCompanyDuplicatesRequest, requestOptions?: Companies.RequestOptions): core.HttpResponsePromise<HomerApi.FindCompanyDuplicatesResponse>;
    private __findCompanyDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
