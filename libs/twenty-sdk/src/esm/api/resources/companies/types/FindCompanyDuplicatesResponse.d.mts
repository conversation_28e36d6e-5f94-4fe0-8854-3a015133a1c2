/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindCompanyDuplicatesResponse {
    data?: FindCompanyDuplicatesResponse.Data.Item[];
}
export declare namespace FindCompanyDuplicatesResponse {
    type Data = Data.Item[];
    namespace Data {
        interface Item {
            totalCount?: number;
            pageInfo?: Item.PageInfo;
            companyDuplicates?: HomerApi.CompanyForResponse[];
        }
        namespace Item {
            interface PageInfo {
                hasNextPage?: boolean;
                startCursor?: string;
                endCursor?: string;
            }
        }
    }
}
