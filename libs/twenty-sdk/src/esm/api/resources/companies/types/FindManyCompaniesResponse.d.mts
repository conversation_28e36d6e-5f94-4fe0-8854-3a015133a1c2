/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyCompaniesResponse {
    data?: FindManyCompaniesResponse.Data;
    pageInfo?: FindManyCompaniesResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyCompaniesResponse {
    interface Data {
        companies?: HomerApi.CompanyForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
