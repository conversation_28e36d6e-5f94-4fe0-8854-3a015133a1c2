/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface UpdateOneConnectedAccountResponse {
    data?: UpdateOneConnectedAccountResponse.Data;
}
export declare namespace UpdateOneConnectedAccountResponse {
    interface Data {
        updateConnectedAccount?: HomerApi.ConnectedAccountForResponse;
    }
}
