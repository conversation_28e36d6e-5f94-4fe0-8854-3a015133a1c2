/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface MessageChannelForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Visibility */
    visibility?: MessageChannelForUpdate.Visibility;
    /** Handle */
    handle?: string;
    /** Channel Type */
    type?: MessageChannelForUpdate.Type;
    /** Is Contact Auto Creation Enabled */
    isContactAutoCreationEnabled?: boolean;
    /** Automatically create People records when receiving or sending emails */
    contactAutoCreationPolicy?: MessageChannelForUpdate.ContactAutoCreationPolicy;
    /** Exclude non professional emails */
    excludeNonProfessionalEmails?: boolean;
    /** Exclude group emails */
    excludeGroupEmails?: boolean;
    /** Is Sync Enabled */
    isSyncEnabled?: boolean;
    /** Last sync cursor */
    syncCursor?: string;
    /** Last sync date */
    syncedAt?: string;
    /** Sync status */
    syncStatus?: MessageChannelForUpdate.SyncStatus;
    /** Sync stage */
    syncStage?: MessageChannelForUpdate.SyncStage;
    /** Sync stage started at */
    syncStageStartedAt?: string;
    /** Throttle Failure Count */
    throttleFailureCount?: number;
    /** Connected Account id foreign key */
    connectedAccountId?: string;
}
export declare namespace MessageChannelForUpdate {
    /**
     * Visibility
     */
    type Visibility = "METADATA" | "SUBJECT" | "SHARE_EVERYTHING";
    const Visibility: {
        readonly Metadata: "METADATA";
        readonly Subject: "SUBJECT";
        readonly ShareEverything: "SHARE_EVERYTHING";
    };
    /**
     * Channel Type
     */
    type Type = "email" | "sms";
    const Type: {
        readonly Email: "email";
        readonly Sms: "sms";
    };
    /**
     * Automatically create People records when receiving or sending emails
     */
    type ContactAutoCreationPolicy = "SENT_AND_RECEIVED" | "SENT" | "NONE";
    const ContactAutoCreationPolicy: {
        readonly SentAndReceived: "SENT_AND_RECEIVED";
        readonly Sent: "SENT";
        readonly None: "NONE";
    };
    /**
     * Sync status
     */
    type SyncStatus = "ONGOING" | "NOT_SYNCED" | "ACTIVE" | "FAILED_INSUFFICIENT_PERMISSIONS" | "FAILED_UNKNOWN";
    const SyncStatus: {
        readonly Ongoing: "ONGOING";
        readonly NotSynced: "NOT_SYNCED";
        readonly Active: "ACTIVE";
        readonly FailedInsufficientPermissions: "FAILED_INSUFFICIENT_PERMISSIONS";
        readonly FailedUnknown: "FAILED_UNKNOWN";
    };
    /**
     * Sync stage
     */
    type SyncStage = "FULL_MESSAGE_LIST_FETCH_PENDING" | "PARTIAL_MESSAGE_LIST_FETCH_PENDING" | "MESSAGE_LIST_FETCH_ONGOING" | "MESSAGES_IMPORT_PENDING" | "MESSAGES_IMPORT_ONGOING" | "FAILED";
    const SyncStage: {
        readonly FullMessageListFetchPending: "FULL_MESSAGE_LIST_FETCH_PENDING";
        readonly PartialMessageListFetchPending: "PARTIAL_MESSAGE_LIST_FETCH_PENDING";
        readonly MessageListFetchOngoing: "MESSAGE_LIST_FETCH_ONGOING";
        readonly MessagesImportPending: "MESSAGES_IMPORT_PENDING";
        readonly MessagesImportOngoing: "MESSAGES_IMPORT_ONGOING";
        readonly Failed: "FAILED";
    };
}
