/**
 * This file was auto-generated by Fern from our API Definition.
 */
export var MessageChannelForUpdate;
(function (MessageChannelForUpdate) {
    MessageChannelForUpdate.Visibility = {
        Metadata: "METADATA",
        Subject: "SUBJECT",
        ShareEverything: "SHARE_EVERYTHING",
    };
    MessageChannelForUpdate.Type = {
        Email: "email",
        Sms: "sms",
    };
    MessageChannelForUpdate.ContactAutoCreationPolicy = {
        SentAndReceived: "SENT_AND_RECEIVED",
        Sent: "SENT",
        None: "NONE",
    };
    MessageChannelForUpdate.SyncStatus = {
        Ongoing: "ONGOING",
        NotSynced: "NOT_SYNCED",
        Active: "ACTIVE",
        FailedInsufficientPermissions: "FAILED_INSUFFICIENT_PERMISSIONS",
        FailedUnknown: "FAILED_UNKNOWN",
    };
    MessageChannelForUpdate.SyncStage = {
        FullMessageListFetchPending: "FULL_MESSAGE_LIST_FETCH_PENDING",
        PartialMessageListFetchPending: "PARTIAL_MESSAGE_LIST_FETCH_PENDING",
        MessageListFetchOngoing: "MESSAGE_LIST_FETCH_ONGOING",
        MessagesImportPending: "MESSAGES_IMPORT_PENDING",
        MessagesImportOngoing: "MESSAGES_IMPORT_ONGOING",
        Failed: "FAILED",
    };
})(MessageChannelForUpdate || (MessageChannelForUpdate = {}));
