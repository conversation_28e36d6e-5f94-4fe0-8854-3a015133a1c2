/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace MessageFolders {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `MessageFolders`
 */
export declare class MessageFolders {
    protected readonly _options: MessageFolders.Options;
    constructor(_options: MessageFolders.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **messageFolders**
     *
     * @param {HomerApi.FindManyMessageFoldersRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.findManyMessageFolders()
     */
    findManyMessageFolders(request?: HomerApi.FindManyMessageFoldersRequest, requestOptions?: MessageFolders.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyMessageFoldersResponse>;
    private __findManyMessageFolders;
    /**
     * @param {HomerApi.CreateOneMessageFolderRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.createOneMessageFolder({
     *         body: {
     *             messageChannelId: "messageChannelId"
     *         }
     *     })
     */
    createOneMessageFolder(request: HomerApi.CreateOneMessageFolderRequest, requestOptions?: MessageFolders.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneMessageFolderResponse>;
    private __createOneMessageFolder;
    /**
     * @param {HomerApi.CreateManyMessageFoldersRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.createManyMessageFolders({
     *         body: [{
     *                 messageChannelId: "messageChannelId"
     *             }]
     *     })
     */
    createManyMessageFolders(request: HomerApi.CreateManyMessageFoldersRequest, requestOptions?: MessageFolders.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyMessageFoldersResponse>;
    private __createManyMessageFolders;
    /**
     * **depth** can be provided to request your **messageFolder**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneMessageFolderRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.findOneMessageFolder("id")
     */
    findOneMessageFolder(id: string, request?: HomerApi.FindOneMessageFolderRequest, requestOptions?: MessageFolders.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneMessageFolderResponse>;
    private __findOneMessageFolder;
    /**
     * @param {string} id - Object id.
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.deleteOneMessageFolder("id")
     */
    deleteOneMessageFolder(id: string, requestOptions?: MessageFolders.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneMessageFolderResponse>;
    private __deleteOneMessageFolder;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.MessageFolderForUpdate} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.updateOneMessageFolder("id")
     */
    updateOneMessageFolder(id: string, request?: HomerApi.MessageFolderForUpdate, requestOptions?: MessageFolders.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneMessageFolderResponse>;
    private __updateOneMessageFolder;
    /**
     * **depth** can be provided to request your **messageFolder**
     *
     * @param {HomerApi.FindMessageFolderDuplicatesRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.findMessageFolderDuplicates()
     */
    findMessageFolderDuplicates(request?: HomerApi.FindMessageFolderDuplicatesRequest, requestOptions?: MessageFolders.RequestOptions): core.HttpResponsePromise<HomerApi.FindMessageFolderDuplicatesResponse>;
    private __findMessageFolderDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
