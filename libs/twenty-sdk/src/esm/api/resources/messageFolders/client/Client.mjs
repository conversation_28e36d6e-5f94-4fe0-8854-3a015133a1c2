/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as HomerApi from "../../../index.mjs";
import { mergeHeaders, mergeOnlyDefinedHeaders } from "../../../../core/headers.mjs";
import urlJoin from "url-join";
import * as errors from "../../../../errors/index.mjs";
/**
 * Object `MessageFolders`
 */
export class MessageFolders {
    constructor(_options) {
        this._options = _options;
    }
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **messageFolders**
     *
     * @param {HomerApi.FindManyMessageFoldersRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.findManyMessageFolders()
     */
    findManyMessageFolders(request = {}, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__findManyMessageFolders(request, requestOptions));
    }
    __findManyMessageFolders() {
        return __awaiter(this, arguments, void 0, function* (request = {}, requestOptions) {
            var _a, _b, _c;
            const { order_by: orderBy, filter, limit, depth, starting_after: startingAfter, ending_before: endingBefore, } = request;
            const _queryParams = {};
            if (orderBy != null) {
                _queryParams["order_by"] = orderBy;
            }
            if (filter != null) {
                _queryParams["filter"] = filter;
            }
            if (limit != null) {
                _queryParams["limit"] = limit.toString();
            }
            if (depth != null) {
                _queryParams["depth"] = depth.toString();
            }
            if (startingAfter != null) {
                _queryParams["starting_after"] = startingAfter;
            }
            if (endingBefore != null) {
                _queryParams["ending_before"] = endingBefore;
            }
            const _response = yield core.fetcher({
                url: urlJoin((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.HomerApiEnvironment.Default, "messageFolders"),
                method: "GET",
                headers: mergeHeaders((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, mergeOnlyDefinedHeaders({ Authorization: yield this._getAuthorizationHeader() }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                queryParameters: _queryParams,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: _response.body,
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new HomerApi.BadRequestError(_response.error.body, _response.rawResponse);
                    case 401:
                        throw new HomerApi.UnauthorizedError(_response.error.body, _response.rawResponse);
                    default:
                        throw new errors.HomerApiError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.HomerApiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.HomerApiTimeoutError("Timeout exceeded when calling GET /messageFolders.");
                case "unknown":
                    throw new errors.HomerApiError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * @param {HomerApi.CreateOneMessageFolderRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.createOneMessageFolder({
     *         body: {
     *             messageChannelId: "messageChannelId"
     *         }
     *     })
     */
    createOneMessageFolder(request, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__createOneMessageFolder(request, requestOptions));
    }
    __createOneMessageFolder(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const { depth, body: _body } = request;
            const _queryParams = {};
            if (depth != null) {
                _queryParams["depth"] = depth.toString();
            }
            const _response = yield core.fetcher({
                url: urlJoin((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.HomerApiEnvironment.Default, "messageFolders"),
                method: "POST",
                headers: mergeHeaders((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, mergeOnlyDefinedHeaders({ Authorization: yield this._getAuthorizationHeader() }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                body: _body,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: _response.body,
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new HomerApi.BadRequestError(_response.error.body, _response.rawResponse);
                    case 401:
                        throw new HomerApi.UnauthorizedError(_response.error.body, _response.rawResponse);
                    default:
                        throw new errors.HomerApiError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.HomerApiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.HomerApiTimeoutError("Timeout exceeded when calling POST /messageFolders.");
                case "unknown":
                    throw new errors.HomerApiError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * @param {HomerApi.CreateManyMessageFoldersRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.createManyMessageFolders({
     *         body: [{
     *                 messageChannelId: "messageChannelId"
     *             }]
     *     })
     */
    createManyMessageFolders(request, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__createManyMessageFolders(request, requestOptions));
    }
    __createManyMessageFolders(request, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const { depth, body: _body } = request;
            const _queryParams = {};
            if (depth != null) {
                _queryParams["depth"] = depth.toString();
            }
            const _response = yield core.fetcher({
                url: urlJoin((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.HomerApiEnvironment.Default, "batch/messageFolders"),
                method: "POST",
                headers: mergeHeaders((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, mergeOnlyDefinedHeaders({ Authorization: yield this._getAuthorizationHeader() }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                body: _body,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: _response.body,
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new HomerApi.BadRequestError(_response.error.body, _response.rawResponse);
                    case 401:
                        throw new HomerApi.UnauthorizedError(_response.error.body, _response.rawResponse);
                    default:
                        throw new errors.HomerApiError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.HomerApiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.HomerApiTimeoutError("Timeout exceeded when calling POST /batch/messageFolders.");
                case "unknown":
                    throw new errors.HomerApiError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * **depth** can be provided to request your **messageFolder**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneMessageFolderRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.findOneMessageFolder("id")
     */
    findOneMessageFolder(id, request = {}, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__findOneMessageFolder(id, request, requestOptions));
    }
    __findOneMessageFolder(id_1) {
        return __awaiter(this, arguments, void 0, function* (id, request = {}, requestOptions) {
            var _a, _b, _c;
            const { depth } = request;
            const _queryParams = {};
            if (depth != null) {
                _queryParams["depth"] = depth.toString();
            }
            const _response = yield core.fetcher({
                url: urlJoin((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.HomerApiEnvironment.Default, `messageFolders/${encodeURIComponent(id)}`),
                method: "GET",
                headers: mergeHeaders((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, mergeOnlyDefinedHeaders({ Authorization: yield this._getAuthorizationHeader() }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                queryParameters: _queryParams,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: _response.body,
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new HomerApi.BadRequestError(_response.error.body, _response.rawResponse);
                    case 401:
                        throw new HomerApi.UnauthorizedError(_response.error.body, _response.rawResponse);
                    default:
                        throw new errors.HomerApiError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.HomerApiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.HomerApiTimeoutError("Timeout exceeded when calling GET /messageFolders/{id}.");
                case "unknown":
                    throw new errors.HomerApiError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * @param {string} id - Object id.
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.deleteOneMessageFolder("id")
     */
    deleteOneMessageFolder(id, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__deleteOneMessageFolder(id, requestOptions));
    }
    __deleteOneMessageFolder(id, requestOptions) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const _response = yield core.fetcher({
                url: urlJoin((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.HomerApiEnvironment.Default, `messageFolders/${encodeURIComponent(id)}`),
                method: "DELETE",
                headers: mergeHeaders((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, mergeOnlyDefinedHeaders({ Authorization: yield this._getAuthorizationHeader() }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: _response.body,
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new HomerApi.BadRequestError(_response.error.body, _response.rawResponse);
                    case 401:
                        throw new HomerApi.UnauthorizedError(_response.error.body, _response.rawResponse);
                    default:
                        throw new errors.HomerApiError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.HomerApiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.HomerApiTimeoutError("Timeout exceeded when calling DELETE /messageFolders/{id}.");
                case "unknown":
                    throw new errors.HomerApiError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.MessageFolderForUpdate} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.updateOneMessageFolder("id")
     */
    updateOneMessageFolder(id, request = {}, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__updateOneMessageFolder(id, request, requestOptions));
    }
    __updateOneMessageFolder(id_1) {
        return __awaiter(this, arguments, void 0, function* (id, request = {}, requestOptions) {
            var _a, _b, _c;
            const { depth } = request, _body = __rest(request, ["depth"]);
            const _queryParams = {};
            if (depth != null) {
                _queryParams["depth"] = depth.toString();
            }
            const _response = yield core.fetcher({
                url: urlJoin((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.HomerApiEnvironment.Default, `messageFolders/${encodeURIComponent(id)}`),
                method: "PATCH",
                headers: mergeHeaders((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, mergeOnlyDefinedHeaders({ Authorization: yield this._getAuthorizationHeader() }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                body: _body,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: _response.body,
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new HomerApi.BadRequestError(_response.error.body, _response.rawResponse);
                    case 401:
                        throw new HomerApi.UnauthorizedError(_response.error.body, _response.rawResponse);
                    default:
                        throw new errors.HomerApiError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.HomerApiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.HomerApiTimeoutError("Timeout exceeded when calling PATCH /messageFolders/{id}.");
                case "unknown":
                    throw new errors.HomerApiError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    /**
     * **depth** can be provided to request your **messageFolder**
     *
     * @param {HomerApi.FindMessageFolderDuplicatesRequest} request
     * @param {MessageFolders.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageFolders.findMessageFolderDuplicates()
     */
    findMessageFolderDuplicates(request = {}, requestOptions) {
        return core.HttpResponsePromise.fromPromise(this.__findMessageFolderDuplicates(request, requestOptions));
    }
    __findMessageFolderDuplicates() {
        return __awaiter(this, arguments, void 0, function* (request = {}, requestOptions) {
            var _a, _b, _c;
            const { depth } = request, _body = __rest(request, ["depth"]);
            const _queryParams = {};
            if (depth != null) {
                _queryParams["depth"] = depth.toString();
            }
            const _response = yield core.fetcher({
                url: urlJoin((_b = (_a = (yield core.Supplier.get(this._options.baseUrl))) !== null && _a !== void 0 ? _a : (yield core.Supplier.get(this._options.environment))) !== null && _b !== void 0 ? _b : environments.HomerApiEnvironment.Default, "messageFolders/duplicates"),
                method: "POST",
                headers: mergeHeaders((_c = this._options) === null || _c === void 0 ? void 0 : _c.headers, mergeOnlyDefinedHeaders({ Authorization: yield this._getAuthorizationHeader() }), requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.headers),
                contentType: "application/json",
                queryParameters: _queryParams,
                requestType: "json",
                body: _body,
                timeoutMs: (requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.timeoutInSeconds) != null ? requestOptions.timeoutInSeconds * 1000 : 60000,
                maxRetries: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.maxRetries,
                abortSignal: requestOptions === null || requestOptions === void 0 ? void 0 : requestOptions.abortSignal,
            });
            if (_response.ok) {
                return {
                    data: _response.body,
                    rawResponse: _response.rawResponse,
                };
            }
            if (_response.error.reason === "status-code") {
                switch (_response.error.statusCode) {
                    case 400:
                        throw new HomerApi.BadRequestError(_response.error.body, _response.rawResponse);
                    case 401:
                        throw new HomerApi.UnauthorizedError(_response.error.body, _response.rawResponse);
                    default:
                        throw new errors.HomerApiError({
                            statusCode: _response.error.statusCode,
                            body: _response.error.body,
                            rawResponse: _response.rawResponse,
                        });
                }
            }
            switch (_response.error.reason) {
                case "non-json":
                    throw new errors.HomerApiError({
                        statusCode: _response.error.statusCode,
                        body: _response.error.rawBody,
                        rawResponse: _response.rawResponse,
                    });
                case "timeout":
                    throw new errors.HomerApiTimeoutError("Timeout exceeded when calling POST /messageFolders/duplicates.");
                case "unknown":
                    throw new errors.HomerApiError({
                        message: _response.error.errorMessage,
                        rawResponse: _response.rawResponse,
                    });
            }
        });
    }
    _getAuthorizationHeader() {
        return __awaiter(this, void 0, void 0, function* () {
            return `Bearer ${yield core.Supplier.get(this._options.token)}`;
        });
    }
}
