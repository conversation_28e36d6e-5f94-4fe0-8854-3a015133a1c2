/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyMessageFoldersResponse {
    data?: FindManyMessageFoldersResponse.Data;
    pageInfo?: FindManyMessageFoldersResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyMessageFoldersResponse {
    interface Data {
        messageFolders?: HomerApi.MessageFolderForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
