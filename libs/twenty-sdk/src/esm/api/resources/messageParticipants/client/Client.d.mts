/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace MessageParticipants {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `MessageParticipants`
 */
export declare class MessageParticipants {
    protected readonly _options: MessageParticipants.Options;
    constructor(_options: MessageParticipants.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **messageParticipants**
     *
     * @param {HomerApi.FindManyMessageParticipantsRequest} request
     * @param {MessageParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageParticipants.findManyMessageParticipants()
     */
    findManyMessageParticipants(request?: HomerApi.FindManyMessageParticipantsRequest, requestOptions?: MessageParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyMessageParticipantsResponse>;
    private __findManyMessageParticipants;
    /**
     * @param {HomerApi.CreateOneMessageParticipantRequest} request
     * @param {MessageParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageParticipants.createOneMessageParticipant({
     *         body: {
     *             messageId: "messageId"
     *         }
     *     })
     */
    createOneMessageParticipant(request: HomerApi.CreateOneMessageParticipantRequest, requestOptions?: MessageParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneMessageParticipantResponse>;
    private __createOneMessageParticipant;
    /**
     * @param {HomerApi.CreateManyMessageParticipantsRequest} request
     * @param {MessageParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageParticipants.createManyMessageParticipants({
     *         body: [{
     *                 messageId: "messageId"
     *             }]
     *     })
     */
    createManyMessageParticipants(request: HomerApi.CreateManyMessageParticipantsRequest, requestOptions?: MessageParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyMessageParticipantsResponse>;
    private __createManyMessageParticipants;
    /**
     * **depth** can be provided to request your **messageParticipant**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneMessageParticipantRequest} request
     * @param {MessageParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageParticipants.findOneMessageParticipant("id")
     */
    findOneMessageParticipant(id: string, request?: HomerApi.FindOneMessageParticipantRequest, requestOptions?: MessageParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneMessageParticipantResponse>;
    private __findOneMessageParticipant;
    /**
     * @param {string} id - Object id.
     * @param {MessageParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageParticipants.deleteOneMessageParticipant("id")
     */
    deleteOneMessageParticipant(id: string, requestOptions?: MessageParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneMessageParticipantResponse>;
    private __deleteOneMessageParticipant;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.MessageParticipantForUpdate} request
     * @param {MessageParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageParticipants.updateOneMessageParticipant("id")
     */
    updateOneMessageParticipant(id: string, request?: HomerApi.MessageParticipantForUpdate, requestOptions?: MessageParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneMessageParticipantResponse>;
    private __updateOneMessageParticipant;
    /**
     * **depth** can be provided to request your **messageParticipant**
     *
     * @param {HomerApi.FindMessageParticipantDuplicatesRequest} request
     * @param {MessageParticipants.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageParticipants.findMessageParticipantDuplicates()
     */
    findMessageParticipantDuplicates(request?: HomerApi.FindMessageParticipantDuplicatesRequest, requestOptions?: MessageParticipants.RequestOptions): core.HttpResponsePromise<HomerApi.FindMessageParticipantDuplicatesResponse>;
    private __findMessageParticipantDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
