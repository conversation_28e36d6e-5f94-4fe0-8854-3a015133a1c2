/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface MessageParticipantForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Role */
    role?: MessageParticipantForUpdate.Role;
    /** Handle */
    handle?: string;
    /** Display Name */
    displayName?: string;
    /** Message id foreign key */
    messageId?: string;
    /** Person id foreign key */
    personId?: string;
    /** Workspace member id foreign key */
    workspaceMemberId?: string;
}
export declare namespace MessageParticipantForUpdate {
    /**
     * Role
     */
    type Role = "from" | "to" | "cc" | "bcc";
    const Role: {
        readonly From: "from";
        readonly To: "to";
        readonly Cc: "cc";
        readonly Bcc: "bcc";
    };
}
