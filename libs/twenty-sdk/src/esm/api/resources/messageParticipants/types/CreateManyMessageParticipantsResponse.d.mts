/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface CreateManyMessageParticipantsResponse {
    data?: CreateManyMessageParticipantsResponse.Data;
}
export declare namespace CreateManyMessageParticipantsResponse {
    interface Data {
        createMessageParticipants?: HomerApi.MessageParticipantForResponse[];
    }
}
