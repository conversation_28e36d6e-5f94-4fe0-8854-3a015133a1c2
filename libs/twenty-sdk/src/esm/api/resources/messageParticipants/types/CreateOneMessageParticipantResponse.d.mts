/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface CreateOneMessageParticipantResponse {
    data?: CreateOneMessageParticipantResponse.Data;
}
export declare namespace CreateOneMessageParticipantResponse {
    interface Data {
        createMessageParticipant?: HomerApi.MessageParticipantForResponse;
    }
}
