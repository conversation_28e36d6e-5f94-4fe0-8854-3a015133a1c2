/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface DeleteOneMessageParticipantResponse {
    data?: DeleteOneMessageParticipantResponse.Data;
}
export declare namespace DeleteOneMessageParticipantResponse {
    interface Data {
        deleteMessageParticipant?: Data.DeleteMessageParticipant;
    }
    namespace Data {
        interface DeleteMessageParticipant {
            id?: string;
        }
    }
}
