/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyMessageParticipantsResponse {
    data?: FindManyMessageParticipantsResponse.Data;
    pageInfo?: FindManyMessageParticipantsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyMessageParticipantsResponse {
    interface Data {
        messageParticipants?: HomerApi.MessageParticipantForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
