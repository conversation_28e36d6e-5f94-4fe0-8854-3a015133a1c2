/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface UpdateOneMessageParticipantResponse {
    data?: UpdateOneMessageParticipantResponse.Data;
}
export declare namespace UpdateOneMessageParticipantResponse {
    interface Data {
        updateMessageParticipant?: HomerApi.MessageParticipantForResponse;
    }
}
