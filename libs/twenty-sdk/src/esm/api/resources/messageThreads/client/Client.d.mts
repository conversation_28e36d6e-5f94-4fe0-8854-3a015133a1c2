/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace MessageThreads {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `MessageThreads`
 */
export declare class MessageThreads {
    protected readonly _options: MessageThreads.Options;
    constructor(_options: MessageThreads.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **messageThreads**
     *
     * @param {HomerApi.FindManyMessageThreadsRequest} request
     * @param {MessageThreads.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageThreads.findManyMessageThreads()
     */
    findManyMessageThreads(request?: HomerApi.FindManyMessageThreadsRequest, requestOptions?: MessageThreads.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyMessageThreadsResponse>;
    private __findManyMessageThreads;
    /**
     * @param {HomerApi.CreateOneMessageThreadRequest} request
     * @param {MessageThreads.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageThreads.createOneMessageThread({
     *         body: {}
     *     })
     */
    createOneMessageThread(request: HomerApi.CreateOneMessageThreadRequest, requestOptions?: MessageThreads.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneMessageThreadResponse>;
    private __createOneMessageThread;
    /**
     * @param {HomerApi.CreateManyMessageThreadsRequest} request
     * @param {MessageThreads.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageThreads.createManyMessageThreads({
     *         body: [{}]
     *     })
     */
    createManyMessageThreads(request: HomerApi.CreateManyMessageThreadsRequest, requestOptions?: MessageThreads.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyMessageThreadsResponse>;
    private __createManyMessageThreads;
    /**
     * **depth** can be provided to request your **messageThread**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneMessageThreadRequest} request
     * @param {MessageThreads.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageThreads.findOneMessageThread("id")
     */
    findOneMessageThread(id: string, request?: HomerApi.FindOneMessageThreadRequest, requestOptions?: MessageThreads.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneMessageThreadResponse>;
    private __findOneMessageThread;
    /**
     * @param {string} id - Object id.
     * @param {MessageThreads.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageThreads.deleteOneMessageThread("id")
     */
    deleteOneMessageThread(id: string, requestOptions?: MessageThreads.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneMessageThreadResponse>;
    private __deleteOneMessageThread;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.MessageThreadForUpdate} request
     * @param {MessageThreads.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageThreads.updateOneMessageThread("id")
     */
    updateOneMessageThread(id: string, request?: HomerApi.MessageThreadForUpdate, requestOptions?: MessageThreads.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneMessageThreadResponse>;
    private __updateOneMessageThread;
    /**
     * **depth** can be provided to request your **messageThread**
     *
     * @param {HomerApi.FindMessageThreadDuplicatesRequest} request
     * @param {MessageThreads.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messageThreads.findMessageThreadDuplicates()
     */
    findMessageThreadDuplicates(request?: HomerApi.FindMessageThreadDuplicatesRequest, requestOptions?: MessageThreads.RequestOptions): core.HttpResponsePromise<HomerApi.FindMessageThreadDuplicatesResponse>;
    private __findMessageThreadDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
