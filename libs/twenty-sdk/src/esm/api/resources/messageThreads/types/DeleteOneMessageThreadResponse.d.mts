/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface DeleteOneMessageThreadResponse {
    data?: DeleteOneMessageThreadResponse.Data;
}
export declare namespace DeleteOneMessageThreadResponse {
    interface Data {
        deleteMessageThread?: Data.DeleteMessageThread;
    }
    namespace Data {
        interface DeleteMessageThread {
            id?: string;
        }
    }
}
