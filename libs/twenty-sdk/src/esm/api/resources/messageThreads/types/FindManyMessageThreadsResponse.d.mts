/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyMessageThreadsResponse {
    data?: FindManyMessageThreadsResponse.Data;
    pageInfo?: FindManyMessageThreadsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyMessageThreadsResponse {
    interface Data {
        messageThreads?: HomerApi.MessageThreadForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
