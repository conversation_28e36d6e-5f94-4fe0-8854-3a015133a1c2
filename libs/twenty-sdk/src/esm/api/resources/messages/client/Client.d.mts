/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace Messages {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Messages`
 */
export declare class Messages {
    protected readonly _options: Messages.Options;
    constructor(_options: Messages.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **messages**
     *
     * @param {HomerApi.FindManyMessagesRequest} request
     * @param {Messages.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messages.findManyMessages()
     */
    findManyMessages(request?: HomerApi.FindManyMessagesRequest, requestOptions?: Messages.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyMessagesResponse>;
    private __findManyMessages;
    /**
     * @param {HomerApi.CreateOneMessageRequest} request
     * @param {Messages.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messages.createOneMessage({
     *         body: {}
     *     })
     */
    createOneMessage(request: HomerApi.CreateOneMessageRequest, requestOptions?: Messages.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneMessageResponse>;
    private __createOneMessage;
    /**
     * @param {HomerApi.CreateManyMessagesRequest} request
     * @param {Messages.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messages.createManyMessages({
     *         body: [{}]
     *     })
     */
    createManyMessages(request: HomerApi.CreateManyMessagesRequest, requestOptions?: Messages.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyMessagesResponse>;
    private __createManyMessages;
    /**
     * **depth** can be provided to request your **message**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneMessageRequest} request
     * @param {Messages.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messages.findOneMessage("id")
     */
    findOneMessage(id: string, request?: HomerApi.FindOneMessageRequest, requestOptions?: Messages.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneMessageResponse>;
    private __findOneMessage;
    /**
     * @param {string} id - Object id.
     * @param {Messages.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messages.deleteOneMessage("id")
     */
    deleteOneMessage(id: string, requestOptions?: Messages.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneMessageResponse>;
    private __deleteOneMessage;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.MessageForUpdate} request
     * @param {Messages.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messages.updateOneMessage("id")
     */
    updateOneMessage(id: string, request?: HomerApi.MessageForUpdate, requestOptions?: Messages.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneMessageResponse>;
    private __updateOneMessage;
    /**
     * **depth** can be provided to request your **message**
     *
     * @param {HomerApi.FindMessageDuplicatesRequest} request
     * @param {Messages.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.messages.findMessageDuplicates()
     */
    findMessageDuplicates(request?: HomerApi.FindMessageDuplicatesRequest, requestOptions?: Messages.RequestOptions): core.HttpResponsePromise<HomerApi.FindMessageDuplicatesResponse>;
    private __findMessageDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
