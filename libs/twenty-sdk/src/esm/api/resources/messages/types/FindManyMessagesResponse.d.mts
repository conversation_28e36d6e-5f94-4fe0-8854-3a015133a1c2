/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyMessagesResponse {
    data?: FindManyMessagesResponse.Data;
    pageInfo?: FindManyMessagesResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyMessagesResponse {
    interface Data {
        messages?: HomerApi.MessageForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
