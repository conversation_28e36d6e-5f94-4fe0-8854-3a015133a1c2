/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace NoteTargets {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `NoteTargets`
 */
export declare class NoteTargets {
    protected readonly _options: NoteTargets.Options;
    constructor(_options: NoteTargets.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **noteTargets**
     *
     * @param {HomerApi.FindManyNoteTargetsRequest} request
     * @param {NoteTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.noteTargets.findManyNoteTargets()
     */
    findManyNoteTargets(request?: HomerApi.FindManyNoteTargetsRequest, requestOptions?: NoteTargets.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyNoteTargetsResponse>;
    private __findManyNoteTargets;
    /**
     * @param {HomerApi.CreateOneNoteTargetRequest} request
     * @param {NoteTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.noteTargets.createOneNoteTarget({
     *         body: {}
     *     })
     */
    createOneNoteTarget(request: HomerApi.CreateOneNoteTargetRequest, requestOptions?: NoteTargets.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneNoteTargetResponse>;
    private __createOneNoteTarget;
    /**
     * @param {HomerApi.CreateManyNoteTargetsRequest} request
     * @param {NoteTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.noteTargets.createManyNoteTargets({
     *         body: [{}]
     *     })
     */
    createManyNoteTargets(request: HomerApi.CreateManyNoteTargetsRequest, requestOptions?: NoteTargets.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyNoteTargetsResponse>;
    private __createManyNoteTargets;
    /**
     * **depth** can be provided to request your **noteTarget**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneNoteTargetRequest} request
     * @param {NoteTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.noteTargets.findOneNoteTarget("id")
     */
    findOneNoteTarget(id: string, request?: HomerApi.FindOneNoteTargetRequest, requestOptions?: NoteTargets.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneNoteTargetResponse>;
    private __findOneNoteTarget;
    /**
     * @param {string} id - Object id.
     * @param {NoteTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.noteTargets.deleteOneNoteTarget("id")
     */
    deleteOneNoteTarget(id: string, requestOptions?: NoteTargets.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneNoteTargetResponse>;
    private __deleteOneNoteTarget;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.NoteTargetForUpdate} request
     * @param {NoteTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.noteTargets.updateOneNoteTarget("id")
     */
    updateOneNoteTarget(id: string, request?: HomerApi.NoteTargetForUpdate, requestOptions?: NoteTargets.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneNoteTargetResponse>;
    private __updateOneNoteTarget;
    /**
     * **depth** can be provided to request your **noteTarget**
     *
     * @param {HomerApi.FindNoteTargetDuplicatesRequest} request
     * @param {NoteTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.noteTargets.findNoteTargetDuplicates()
     */
    findNoteTargetDuplicates(request?: HomerApi.FindNoteTargetDuplicatesRequest, requestOptions?: NoteTargets.RequestOptions): core.HttpResponsePromise<HomerApi.FindNoteTargetDuplicatesResponse>;
    private __findNoteTargetDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
