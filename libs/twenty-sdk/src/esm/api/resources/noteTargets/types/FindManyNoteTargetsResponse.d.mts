/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyNoteTargetsResponse {
    data?: FindManyNoteTargetsResponse.Data;
    pageInfo?: FindManyNoteTargetsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyNoteTargetsResponse {
    interface Data {
        noteTargets?: HomerApi.NoteTargetForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
