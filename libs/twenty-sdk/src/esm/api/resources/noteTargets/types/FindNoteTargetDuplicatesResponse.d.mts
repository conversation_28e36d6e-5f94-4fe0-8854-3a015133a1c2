/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindNoteTargetDuplicatesResponse {
    data?: FindNoteTargetDuplicatesResponse.Data.Item[];
}
export declare namespace FindNoteTargetDuplicatesResponse {
    type Data = Data.Item[];
    namespace Data {
        interface Item {
            totalCount?: number;
            pageInfo?: Item.PageInfo;
            companyDuplicates?: HomerApi.NoteTargetForResponse[];
        }
        namespace Item {
            interface PageInfo {
                hasNextPage?: boolean;
                startCursor?: string;
                endCursor?: string;
            }
        }
    }
}
