/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON>pi from "../../../index.mjs";
export declare namespace Notes {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Notes`
 */
export declare class Notes {
    protected readonly _options: Notes.Options;
    constructor(_options: Notes.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **notes**
     *
     * @param {HomerApi.FindManyNotesRequest} request
     * @param {Notes.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.notes.findManyNotes()
     */
    findManyNotes(request?: HomerApi.FindManyNotesRequest, requestOptions?: Notes.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyNotesResponse>;
    private __findManyNotes;
    /**
     * @param {HomerApi.CreateOneNoteRequest} request
     * @param {Notes.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.notes.createOneNote({
     *         body: {}
     *     })
     */
    createOneNote(request: HomerApi.CreateOneNoteRequest, requestOptions?: Notes.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneNoteResponse>;
    private __createOneNote;
    /**
     * @param {HomerApi.CreateManyNotesRequest} request
     * @param {Notes.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.notes.createManyNotes({
     *         body: [{}]
     *     })
     */
    createManyNotes(request: HomerApi.CreateManyNotesRequest, requestOptions?: Notes.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyNotesResponse>;
    private __createManyNotes;
    /**
     * **depth** can be provided to request your **note**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneNoteRequest} request
     * @param {Notes.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.notes.findOneNote("id")
     */
    findOneNote(id: string, request?: HomerApi.FindOneNoteRequest, requestOptions?: Notes.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneNoteResponse>;
    private __findOneNote;
    /**
     * @param {string} id - Object id.
     * @param {Notes.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.notes.deleteOneNote("id")
     */
    deleteOneNote(id: string, requestOptions?: Notes.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneNoteResponse>;
    private __deleteOneNote;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.NoteForUpdate} request
     * @param {Notes.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.notes.updateOneNote("id")
     */
    updateOneNote(id: string, request?: HomerApi.NoteForUpdate, requestOptions?: Notes.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneNoteResponse>;
    private __updateOneNote;
    /**
     * **depth** can be provided to request your **note**
     *
     * @param {HomerApi.FindNoteDuplicatesRequest} request
     * @param {Notes.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.notes.findNoteDuplicates()
     */
    findNoteDuplicates(request?: HomerApi.FindNoteDuplicatesRequest, requestOptions?: Notes.RequestOptions): core.HttpResponsePromise<HomerApi.FindNoteDuplicatesResponse>;
    private __findNoteDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
