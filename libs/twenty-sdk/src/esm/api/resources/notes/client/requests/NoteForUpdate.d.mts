/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface NoteForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Note record position */
    position?: number;
    /** Note title */
    title?: string;
    /** Note body */
    body?: string;
    /** Note body */
    bodyV2?: NoteForUpdate.BodyV2;
    /** The creator of the record */
    createdBy?: NoteForUpdate.CreatedBy;
    /** Note type */
    noteType?: NoteForUpdate.NoteType;
}
export declare namespace NoteForUpdate {
    /**
     * Note body
     */
    interface BodyV2 {
        blocknote?: string;
        markdown?: string;
    }
    /**
     * The creator of the record
     */
    interface CreatedBy {
        source?: CreatedBy.Source;
    }
    namespace CreatedBy {
        type Source = "EMAIL" | "CALENDAR" | "WORKFLOW" | "API" | "IMPORT" | "MANUAL" | "SYSTEM";
        const Source: {
            readonly Email: "EMAIL";
            readonly Calendar: "CALENDAR";
            readonly Workflow: "WORKFLOW";
            readonly Api: "API";
            readonly Import: "IMPORT";
            readonly Manual: "MANUAL";
            readonly System: "SYSTEM";
        };
    }
    /**
     * Note type
     */
    type NoteType = "NONE" | "PROMPT" | "PROMPT_LEAD" | "PROMPT_REAL_ESTATE_AGENT" | "PROPERTY_DOC_PUBLIC" | "PROPERTY_DOC_PRIVATE";
    const NoteType: {
        readonly None: "NONE";
        readonly Prompt: "PROMPT";
        readonly PromptLead: "PROMPT_LEAD";
        readonly PromptRealEstateAgent: "PROMPT_REAL_ESTATE_AGENT";
        readonly PropertyDocPublic: "PROPERTY_DOC_PUBLIC";
        readonly PropertyDocPrivate: "PROPERTY_DOC_PRIVATE";
    };
}
