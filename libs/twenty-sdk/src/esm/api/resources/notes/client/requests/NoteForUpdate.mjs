/**
 * This file was auto-generated by Fern from our API Definition.
 */
export var NoteForUpdate;
(function (NoteForUpdate) {
    let CreatedBy;
    (function (CreatedBy) {
        CreatedBy.Source = {
            Email: "EMAIL",
            Calendar: "CALENDAR",
            Workflow: "WORKFLOW",
            Api: "API",
            Import: "IMPORT",
            Manual: "MANUAL",
            System: "SYSTEM",
        };
    })(CreatedBy = NoteForUpdate.CreatedBy || (NoteForUpdate.CreatedBy = {}));
    NoteForUpdate.NoteType = {
        None: "NONE",
        Prompt: "PROMPT",
        PromptLead: "PROMPT_LEAD",
        PromptRealEstateAgent: "PROMPT_REAL_ESTATE_AGENT",
        PropertyDocPublic: "PROPERTY_DOC_PUBLIC",
        PropertyDocPrivate: "PROPERTY_DOC_PRIVATE",
    };
})(NoteForUpdate || (NoteForUpdate = {}));
