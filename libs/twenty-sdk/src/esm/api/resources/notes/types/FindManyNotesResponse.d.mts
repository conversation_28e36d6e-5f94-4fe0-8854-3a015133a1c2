/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyNotesResponse {
    data?: FindManyNotesResponse.Data;
    pageInfo?: FindManyNotesResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyNotesResponse {
    interface Data {
        notes?: HomerApi.NoteForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
