/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindNoteDuplicatesResponse {
    data?: FindNoteDuplicatesResponse.Data.Item[];
}
export declare namespace FindNoteDuplicatesResponse {
    type Data = Data.Item[];
    namespace Data {
        interface Item {
            totalCount?: number;
            pageInfo?: Item.PageInfo;
            companyDuplicates?: HomerApi.NoteForResponse[];
        }
        namespace Item {
            interface PageInfo {
                hasNextPage?: boolean;
                startCursor?: string;
                endCursor?: string;
            }
        }
    }
}
