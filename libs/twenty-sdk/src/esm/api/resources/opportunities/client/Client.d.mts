/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace Opportunities {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Opportunities`
 */
export declare class Opportunities {
    protected readonly _options: Opportunities.Options;
    constructor(_options: Opportunities.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **opportunities**
     *
     * @param {HomerApi.FindManyOpportunitiesRequest} request
     * @param {Opportunities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.opportunities.findManyOpportunities()
     */
    findManyOpportunities(request?: HomerApi.FindManyOpportunitiesRequest, requestOptions?: Opportunities.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyOpportunitiesResponse>;
    private __findManyOpportunities;
    /**
     * @param {HomerApi.CreateOneOpportunityRequest} request
     * @param {Opportunities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.opportunities.createOneOpportunity({
     *         body: {}
     *     })
     */
    createOneOpportunity(request: HomerApi.CreateOneOpportunityRequest, requestOptions?: Opportunities.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneOpportunityResponse>;
    private __createOneOpportunity;
    /**
     * @param {HomerApi.CreateManyOpportunitiesRequest} request
     * @param {Opportunities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.opportunities.createManyOpportunities({
     *         body: [{}]
     *     })
     */
    createManyOpportunities(request: HomerApi.CreateManyOpportunitiesRequest, requestOptions?: Opportunities.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyOpportunitiesResponse>;
    private __createManyOpportunities;
    /**
     * **depth** can be provided to request your **opportunity**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneOpportunityRequest} request
     * @param {Opportunities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.opportunities.findOneOpportunity("id")
     */
    findOneOpportunity(id: string, request?: HomerApi.FindOneOpportunityRequest, requestOptions?: Opportunities.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneOpportunityResponse>;
    private __findOneOpportunity;
    /**
     * @param {string} id - Object id.
     * @param {Opportunities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.opportunities.deleteOneOpportunity("id")
     */
    deleteOneOpportunity(id: string, requestOptions?: Opportunities.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneOpportunityResponse>;
    private __deleteOneOpportunity;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.OpportunityForUpdate} request
     * @param {Opportunities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.opportunities.updateOneOpportunity("id")
     */
    updateOneOpportunity(id: string, request?: HomerApi.OpportunityForUpdate, requestOptions?: Opportunities.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneOpportunityResponse>;
    private __updateOneOpportunity;
    /**
     * **depth** can be provided to request your **opportunity**
     *
     * @param {HomerApi.FindOpportunityDuplicatesRequest} request
     * @param {Opportunities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.opportunities.findOpportunityDuplicates()
     */
    findOpportunityDuplicates(request?: HomerApi.FindOpportunityDuplicatesRequest, requestOptions?: Opportunities.RequestOptions): core.HttpResponsePromise<HomerApi.FindOpportunityDuplicatesResponse>;
    private __findOpportunityDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
