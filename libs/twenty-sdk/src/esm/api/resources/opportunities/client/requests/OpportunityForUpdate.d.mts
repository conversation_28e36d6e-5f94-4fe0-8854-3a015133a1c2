/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface OpportunityForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** The opportunity name */
    name?: string;
    /** Opportunity amount */
    amount?: OpportunityForUpdate.Amount;
    /** Opportunity close date */
    closeDate?: string;
    /** Opportunity stage */
    stage?: OpportunityForUpdate.Stage;
    /** Opportunity record position */
    position?: number;
    /** The creator of the record */
    createdBy?: OpportunityForUpdate.CreatedBy;
    /** Opportunity type */
    opportunityType?: "PROPERTY_SALE";
    /** Stage details */
    stageDetails?: string;
    /** Proactive messages */
    proactiveMessages?: number;
    /** Opportunity classification */
    opportunityClassification?: OpportunityForUpdate.OpportunityClassification;
    /** Opportunity point of contact id foreign key */
    pointOfContactId?: string;
    /** Opportunity company id foreign key */
    companyId?: string;
    /** Opportunity property id foreign key */
    propertyId?: string;
}
export declare namespace OpportunityForUpdate {
    /**
     * Opportunity amount
     */
    interface Amount {
        amountMicros?: number;
        currencyCode?: string;
    }
    /**
     * Opportunity stage
     */
    type Stage = "PENDING_CONTACT" | "CONTACT_ATTEMPT" | "CONTACT_IN_PROGRESS" | "QUALIFIED" | "UNQUALIFIED" | "REASSESSMENT" | "OPT_OUT";
    const Stage: {
        readonly PendingContact: "PENDING_CONTACT";
        readonly ContactAttempt: "CONTACT_ATTEMPT";
        readonly ContactInProgress: "CONTACT_IN_PROGRESS";
        readonly Qualified: "QUALIFIED";
        readonly Unqualified: "UNQUALIFIED";
        readonly Reassessment: "REASSESSMENT";
        readonly OptOut: "OPT_OUT";
    };
    /**
     * The creator of the record
     */
    interface CreatedBy {
        source?: CreatedBy.Source;
    }
    namespace CreatedBy {
        type Source = "EMAIL" | "CALENDAR" | "WORKFLOW" | "API" | "IMPORT" | "MANUAL" | "SYSTEM";
        const Source: {
            readonly Email: "EMAIL";
            readonly Calendar: "CALENDAR";
            readonly Workflow: "WORKFLOW";
            readonly Api: "API";
            readonly Import: "IMPORT";
            readonly Manual: "MANUAL";
            readonly System: "SYSTEM";
        };
    }
    /**
     * Opportunity classification
     */
    type OpportunityClassification = "COLD" | "WARM" | "HOT";
    const OpportunityClassification: {
        readonly Cold: "COLD";
        readonly Warm: "WARM";
        readonly Hot: "HOT";
    };
}
