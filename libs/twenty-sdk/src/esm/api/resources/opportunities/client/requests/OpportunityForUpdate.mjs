/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
export var OpportunityForUpdate;
(function (OpportunityForUpdate) {
    OpportunityForUpdate.Stage = {
        PendingContact: "PENDING_CONTACT",
        ContactAttempt: "CONTACT_ATTEMPT",
        ContactInProgress: "CONTACT_IN_PROGRESS",
        Qualified: "QUALIFIED",
        Unqualified: "UNQUALIFIED",
        Reassessment: "REASSESSMENT",
        OptOut: "OPT_OUT",
    };
    let CreatedBy;
    (function (CreatedBy) {
        CreatedBy.Source = {
            Email: "EMAIL",
            Calendar: "CALENDAR",
            Workflow: "WORKFLOW",
            Api: "API",
            Import: "IMPORT",
            Manual: "MANUAL",
            System: "SYSTEM",
        };
    })(CreatedBy = OpportunityForUpdate.CreatedBy || (OpportunityForUpdate.CreatedBy = {}));
    OpportunityForUpdate.OpportunityClassification = {
        Cold: "COLD",
        Warm: "WARM",
        Hot: "HOT",
    };
})(OpportunityForUpdate || (OpportunityForUpdate = {}));
