/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface DeleteOneOpportunityResponse {
    data?: DeleteOneOpportunityResponse.Data;
}
export declare namespace DeleteOneOpportunityResponse {
    interface Data {
        deleteOpportunity?: Data.DeleteOpportunity;
    }
    namespace Data {
        interface DeleteOpportunity {
            id?: string;
        }
    }
}
