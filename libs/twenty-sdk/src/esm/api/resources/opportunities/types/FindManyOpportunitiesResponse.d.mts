/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyOpportunitiesResponse {
    data?: FindManyOpportunitiesResponse.Data;
    pageInfo?: FindManyOpportunitiesResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyOpportunitiesResponse {
    interface Data {
        opportunities?: HomerApi.OpportunityForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
