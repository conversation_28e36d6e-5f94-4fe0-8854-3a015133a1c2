/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace People {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `People`
 */
export declare class People {
    protected readonly _options: People.Options;
    constructor(_options: People.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **people**
     *
     * @param {HomerApi.FindManyPeopleRequest} request
     * @param {People.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.people.findManyPeople()
     */
    findManyPeople(request?: HomerApi.FindManyPeopleRequest, requestOptions?: People.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyPeopleResponse>;
    private __findManyPeople;
    /**
     * @param {HomerApi.CreateOnePersonRequest} request
     * @param {People.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.people.createOnePerson({
     *         body: {}
     *     })
     */
    createOnePerson(request: HomerApi.CreateOnePersonRequest, requestOptions?: People.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOnePersonResponse>;
    private __createOnePerson;
    /**
     * @param {HomerApi.CreateManyPeopleRequest} request
     * @param {People.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.people.createManyPeople({
     *         body: [{}]
     *     })
     */
    createManyPeople(request: HomerApi.CreateManyPeopleRequest, requestOptions?: People.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyPeopleResponse>;
    private __createManyPeople;
    /**
     * **depth** can be provided to request your **person**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOnePersonRequest} request
     * @param {People.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.people.findOnePerson("id")
     */
    findOnePerson(id: string, request?: HomerApi.FindOnePersonRequest, requestOptions?: People.RequestOptions): core.HttpResponsePromise<HomerApi.FindOnePersonResponse>;
    private __findOnePerson;
    /**
     * @param {string} id - Object id.
     * @param {People.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.people.deleteOnePerson("id")
     */
    deleteOnePerson(id: string, requestOptions?: People.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOnePersonResponse>;
    private __deleteOnePerson;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.PersonForUpdate} request
     * @param {People.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.people.updateOnePerson("id")
     */
    updateOnePerson(id: string, request?: HomerApi.PersonForUpdate, requestOptions?: People.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOnePersonResponse>;
    private __updateOnePerson;
    /**
     * **depth** can be provided to request your **person**
     *
     * @param {HomerApi.FindPersonDuplicatesRequest} request
     * @param {People.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.people.findPersonDuplicates()
     */
    findPersonDuplicates(request?: HomerApi.FindPersonDuplicatesRequest, requestOptions?: People.RequestOptions): core.HttpResponsePromise<HomerApi.FindPersonDuplicatesResponse>;
    private __findPersonDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
