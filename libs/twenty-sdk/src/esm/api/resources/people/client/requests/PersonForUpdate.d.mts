/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface PersonForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Contact’s name */
    name?: PersonForUpdate.Name;
    /** Contact’s Emails */
    emails?: PersonForUpdate.Emails;
    /** Contact’s Linkedin account */
    linkedinLink?: PersonForUpdate.LinkedinLink;
    /** Contact’s X/Twitter account */
    xLink?: PersonForUpdate.XLink;
    /** Contact’s job title */
    jobTitle?: string;
    /** Contact’s phone numbers */
    phones?: PersonForUpdate.Phones;
    /** Contact’s city */
    city?: string;
    /** Contact’s avatar */
    avatarUrl?: string;
    /** Person record Position */
    position?: number;
    /** The creator of the record */
    createdBy?: PersonForUpdate.CreatedBy;
    /** Communication type */
    communicationType?: PersonForUpdate.CommunicationType;
    /** communicationExternalId */
    communicationExternalId?: string;
    /** Communication settings */
    communicationSettings?: Record<string, unknown>;
    /** Person role */
    personRole?: PersonForUpdate.PersonRole;
    /** Integration type */
    integrationType?: "MANUAL";
    /** Integration external id */
    integrationExternalId?: string;
    /** Property integration type */
    propertyIntegrationType?: PersonForUpdate.PropertyIntegrationType;
    /** Property integration key */
    propertyIntegrationKey?: string;
    /** Will Combine Income */
    willCombineIncome?: boolean;
    /** Income type */
    incomeType?: PersonForUpdate.IncomeType;
    /** Family income */
    familyIncome?: PersonForUpdate.FamilyIncome;
    /** Region of interest */
    regionOfInterest?: string;
    /** Birth year */
    birthYear?: number;
    /** Birth month */
    birthMonth?: number;
    /** Birth day */
    birthDay?: number;
    /** Creci number */
    creciNumber?: string;
    /** Creci UF */
    creciUf?: string;
    /** Contact’s company id foreign key */
    companyId?: string;
}
export declare namespace PersonForUpdate {
    /**
     * Contact’s name
     */
    interface Name {
        firstName?: string;
        lastName?: string;
    }
    /**
     * Contact’s Emails
     */
    interface Emails {
        primaryEmail?: string;
        additionalEmails?: string[];
    }
    /**
     * Contact’s Linkedin account
     */
    interface LinkedinLink {
        primaryLinkLabel?: string;
        primaryLinkUrl?: string;
        secondaryLinks?: LinkedinLink.SecondaryLinks.Item[];
    }
    namespace LinkedinLink {
        type SecondaryLinks = SecondaryLinks.Item[];
        namespace SecondaryLinks {
            /**
             * A secondary link
             */
            interface Item {
                url?: string;
                label?: string;
            }
        }
    }
    /**
     * Contact’s X/Twitter account
     */
    interface XLink {
        primaryLinkLabel?: string;
        primaryLinkUrl?: string;
        secondaryLinks?: XLink.SecondaryLinks.Item[];
    }
    namespace XLink {
        type SecondaryLinks = SecondaryLinks.Item[];
        namespace SecondaryLinks {
            /**
             * A secondary link
             */
            interface Item {
                url?: string;
                label?: string;
            }
        }
    }
    /**
     * Contact’s phone numbers
     */
    interface Phones {
        additionalPhones?: string[];
        primaryPhoneCountryCode?: string;
        primaryPhoneCallingCode?: string;
        primaryPhoneNumber?: string;
    }
    /**
     * The creator of the record
     */
    interface CreatedBy {
        source?: CreatedBy.Source;
    }
    namespace CreatedBy {
        type Source = "EMAIL" | "CALENDAR" | "WORKFLOW" | "API" | "IMPORT" | "MANUAL" | "SYSTEM";
        const Source: {
            readonly Email: "EMAIL";
            readonly Calendar: "CALENDAR";
            readonly Workflow: "WORKFLOW";
            readonly Api: "API";
            readonly Import: "IMPORT";
            readonly Manual: "MANUAL";
            readonly System: "SYSTEM";
        };
    }
    /**
     * Communication type
     */
    type CommunicationType = "NONE" | "TWILIO_WHATSAPP" | "MAYTAPI";
    const CommunicationType: {
        readonly None: "NONE";
        readonly TwilioWhatsapp: "TWILIO_WHATSAPP";
        readonly Maytapi: "MAYTAPI";
    };
    /**
     * Person role
     */
    type PersonRole = "LEAD" | "REAL_ESTATE_AGENT" | "ADMIN" | "OWNER" | "AI_AGENT";
    const PersonRole: {
        readonly Lead: "LEAD";
        readonly RealEstateAgent: "REAL_ESTATE_AGENT";
        readonly Admin: "ADMIN";
        readonly Owner: "OWNER";
        readonly AiAgent: "AI_AGENT";
    };
    /**
     * Property integration type
     */
    type PropertyIntegrationType = "MANUAL" | "ZAP";
    const PropertyIntegrationType: {
        readonly Manual: "MANUAL";
        readonly Zap: "ZAP";
    };
    /**
     * Income type
     */
    type IncomeType = "FORMAL" | "UNVERIFIED_INFORMAL" | "VERIFIED_INFORMAL";
    const IncomeType: {
        readonly Formal: "FORMAL";
        readonly UnverifiedInformal: "UNVERIFIED_INFORMAL";
        readonly VerifiedInformal: "VERIFIED_INFORMAL";
    };
    /**
     * Family income
     */
    interface FamilyIncome {
        amountMicros?: number;
        currencyCode?: string;
    }
}
