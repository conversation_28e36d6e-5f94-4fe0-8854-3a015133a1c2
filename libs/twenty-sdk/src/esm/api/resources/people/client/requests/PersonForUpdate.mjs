/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
export var PersonForUpdate;
(function (PersonForUpdate) {
    let CreatedBy;
    (function (CreatedBy) {
        CreatedBy.Source = {
            Email: "EMAIL",
            Calendar: "CALENDAR",
            Workflow: "WORKFLOW",
            Api: "API",
            Import: "IMPORT",
            Manual: "MANUAL",
            System: "SYSTEM",
        };
    })(CreatedBy = PersonForUpdate.CreatedBy || (PersonForUpdate.CreatedBy = {}));
    PersonForUpdate.CommunicationType = {
        None: "NONE",
        TwilioWhatsapp: "TWILIO_WHATSAPP",
        Maytapi: "MAYTAPI",
    };
    PersonForUpdate.PersonRole = {
        Lead: "LEAD",
        RealEstateAgent: "REAL_ESTATE_AGENT",
        Admin: "ADMIN",
        Owner: "OWNER",
        AiAgent: "AI_AGENT",
    };
    PersonForUpdate.PropertyIntegrationType = {
        Manual: "MANUAL",
        Zap: "ZAP",
    };
    PersonForUpdate.IncomeType = {
        Formal: "FORMAL",
        UnverifiedInformal: "UNVERIFIED_INFORMAL",
        VerifiedInformal: "VERIFIED_INFORMAL",
    };
})(PersonForUpdate || (PersonForUpdate = {}));
