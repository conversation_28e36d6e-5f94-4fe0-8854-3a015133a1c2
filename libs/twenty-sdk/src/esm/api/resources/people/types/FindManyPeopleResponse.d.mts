/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyPeopleResponse {
    data?: FindManyPeopleResponse.Data;
    pageInfo?: FindManyPeopleResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyPeopleResponse {
    interface Data {
        people?: HomerApi.PersonForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
