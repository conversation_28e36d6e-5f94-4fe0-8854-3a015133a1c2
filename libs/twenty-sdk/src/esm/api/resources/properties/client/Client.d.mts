/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON>pi from "../../../index.mjs";
export declare namespace Properties {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Properties`
 */
export declare class Properties {
    protected readonly _options: Properties.Options;
    constructor(_options: Properties.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **properties**
     *
     * @param {HomerApi.FindManyPropertiesRequest} request
     * @param {Properties.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.properties.findManyProperties()
     */
    findManyProperties(request?: HomerApi.FindManyPropertiesRequest, requestOptions?: Properties.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyPropertiesResponse>;
    private __findManyProperties;
    /**
     * @param {HomerApi.CreateOnePropertyRequest} request
     * @param {Properties.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.properties.createOneProperty({
     *         body: {}
     *     })
     */
    createOneProperty(request: HomerApi.CreateOnePropertyRequest, requestOptions?: Properties.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOnePropertyResponse>;
    private __createOneProperty;
    /**
     * @param {HomerApi.CreateManyPropertiesRequest} request
     * @param {Properties.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.properties.createManyProperties({
     *         body: [{}]
     *     })
     */
    createManyProperties(request: HomerApi.CreateManyPropertiesRequest, requestOptions?: Properties.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyPropertiesResponse>;
    private __createManyProperties;
    /**
     * **depth** can be provided to request your **property**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOnePropertyRequest} request
     * @param {Properties.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.properties.findOneProperty("id")
     */
    findOneProperty(id: string, request?: HomerApi.FindOnePropertyRequest, requestOptions?: Properties.RequestOptions): core.HttpResponsePromise<HomerApi.FindOnePropertyResponse>;
    private __findOneProperty;
    /**
     * @param {string} id - Object id.
     * @param {Properties.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.properties.deleteOneProperty("id")
     */
    deleteOneProperty(id: string, requestOptions?: Properties.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOnePropertyResponse>;
    private __deleteOneProperty;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.PropertyForUpdate} request
     * @param {Properties.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.properties.updateOneProperty("id")
     */
    updateOneProperty(id: string, request?: HomerApi.PropertyForUpdate, requestOptions?: Properties.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOnePropertyResponse>;
    private __updateOneProperty;
    /**
     * **depth** can be provided to request your **property**
     *
     * @param {HomerApi.FindPropertyDuplicatesRequest} request
     * @param {Properties.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.properties.findPropertyDuplicates()
     */
    findPropertyDuplicates(request?: HomerApi.FindPropertyDuplicatesRequest, requestOptions?: Properties.RequestOptions): core.HttpResponsePromise<HomerApi.FindPropertyDuplicatesResponse>;
    private __findPropertyDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
