/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface PropertyForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Property description */
    description?: string;
    /** Property record Position */
    position?: number;
    /** The property name */
    name?: string;
    /** Property code */
    code?: string;
    /** Integration type */
    integrationType?: PropertyForUpdate.IntegrationType;
    /** Property Integration Key */
    integrationKey?: string;
    /** Property Integration External Id */
    integrationExternalId?: string;
    /** The creator of the record */
    createdBy?: PropertyForUpdate.CreatedBy;
    /** Property type */
    propertyType?: PropertyForUpdate.PropertyType;
    /** Construction status */
    constructionStatus?: PropertyForUpdate.ConstructionStatus;
    /** Listing type */
    listingType?: PropertyForUpdate.ListingType;
    /** Price */
    price?: PropertyForUpdate.Price;
    /** Condo fee */
    condoFee?: PropertyForUpdate.CondoFee;
    /** Yearly tax */
    yearlyTax?: PropertyForUpdate.YearlyTax;
    /** Constructed area m2 */
    constructedAreaM2?: number;
    /** Lot area m2 */
    lotAreaM2?: number;
    /** Living area m2 */
    livingAreaM2?: number;
    /** Bedrooms */
    bedrooms?: number;
    /** Bathrooms */
    bathrooms?: number;
    /** Suites */
    suites?: number;
    /** Garage */
    garage?: number;
    /** Floors */
    floors?: number;
    /** Features */
    features?: string[];
    /** Address of the property */
    address?: PropertyForUpdate.Address;
    /** Property company id foreign key */
    companyId?: string;
    /** Person responsible for contact the property id foreign key */
    contactPersonId?: string;
}
export declare namespace PropertyForUpdate {
    /**
     * Integration type
     */
    type IntegrationType = "MANUAL" | "ZAP";
    const IntegrationType: {
        readonly Manual: "MANUAL";
        readonly Zap: "ZAP";
    };
    /**
     * The creator of the record
     */
    interface CreatedBy {
        source?: CreatedBy.Source;
    }
    namespace CreatedBy {
        type Source = "EMAIL" | "CALENDAR" | "WORKFLOW" | "API" | "IMPORT" | "MANUAL" | "SYSTEM";
        const Source: {
            readonly Email: "EMAIL";
            readonly Calendar: "CALENDAR";
            readonly Workflow: "WORKFLOW";
            readonly Api: "API";
            readonly Import: "IMPORT";
            readonly Manual: "MANUAL";
            readonly System: "SYSTEM";
        };
    }
    /**
     * Property type
     */
    type PropertyType = "APARTMENT" | "SINGLE_FAMILY_HOME" | "COMMERCIAL_BUILDING";
    const PropertyType: {
        readonly Apartment: "APARTMENT";
        readonly SingleFamilyHome: "SINGLE_FAMILY_HOME";
        readonly CommercialBuilding: "COMMERCIAL_BUILDING";
    };
    /**
     * Construction status
     */
    type ConstructionStatus = "PRE_CONSTRUCTION" | "UNDER_CONSTRUCTION" | "NEW_CONSTRUCTION" | "RESALE";
    const ConstructionStatus: {
        readonly PreConstruction: "PRE_CONSTRUCTION";
        readonly UnderConstruction: "UNDER_CONSTRUCTION";
        readonly NewConstruction: "NEW_CONSTRUCTION";
        readonly Resale: "RESALE";
    };
    /**
     * Listing type
     */
    type ListingType = "SALE" | "RENT";
    const ListingType: {
        readonly Sale: "SALE";
        readonly Rent: "RENT";
    };
    /**
     * Price
     */
    interface Price {
        amountMicros?: number;
        currencyCode?: string;
    }
    /**
     * Condo fee
     */
    interface CondoFee {
        amountMicros?: number;
        currencyCode?: string;
    }
    /**
     * Yearly tax
     */
    interface YearlyTax {
        amountMicros?: number;
        currencyCode?: string;
    }
    /**
     * Address of the property
     */
    interface Address {
        addressStreet1?: string;
        addressStreet2?: string;
        addressCity?: string;
        addressPostcode?: string;
        addressState?: string;
        addressCountry?: string;
        addressLat?: number;
        addressLng?: number;
    }
}
