/**
 * This file was auto-generated by Fern from our API Definition.
 */
export var PropertyForUpdate;
(function (PropertyForUpdate) {
    PropertyForUpdate.IntegrationType = {
        Manual: "MANUAL",
        Zap: "ZAP",
    };
    let CreatedBy;
    (function (CreatedBy) {
        CreatedBy.Source = {
            Email: "EMAIL",
            Calendar: "CALENDAR",
            Workflow: "WORKFLOW",
            Api: "API",
            Import: "IMPORT",
            Manual: "MANUAL",
            System: "SYSTEM",
        };
    })(CreatedBy = PropertyForUpdate.CreatedBy || (PropertyForUpdate.CreatedBy = {}));
    PropertyForUpdate.PropertyType = {
        Apartment: "APARTMENT",
        SingleFamilyHome: "SINGLE_FAMILY_HOME",
        CommercialBuilding: "COMMERCIAL_BUILDING",
    };
    PropertyForUpdate.ConstructionStatus = {
        PreConstruction: "PRE_CONSTRUCTION",
        UnderConstruction: "UNDER_CONSTRUCTION",
        NewConstruction: "NEW_CONSTRUCTION",
        Resale: "RESALE",
    };
    PropertyForUpdate.ListingType = {
        Sale: "SALE",
        Rent: "RENT",
    };
})(PropertyForUpdate || (PropertyForUpdate = {}));
