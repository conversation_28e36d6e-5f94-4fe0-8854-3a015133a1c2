/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyPropertiesResponse {
    data?: FindManyPropertiesResponse.Data;
    pageInfo?: FindManyPropertiesResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyPropertiesResponse {
    interface Data {
        properties?: HomerApi.PropertyForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
