/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindPropertyDuplicatesResponse {
    data?: FindPropertyDuplicatesResponse.Data.Item[];
}
export declare namespace FindPropertyDuplicatesResponse {
    type Data = Data.Item[];
    namespace Data {
        interface Item {
            totalCount?: number;
            pageInfo?: Item.PageInfo;
            companyDuplicates?: HomerApi.PropertyForResponse[];
        }
        namespace Item {
            interface PageInfo {
                hasNextPage?: boolean;
                startCursor?: string;
                endCursor?: string;
            }
        }
    }
}
