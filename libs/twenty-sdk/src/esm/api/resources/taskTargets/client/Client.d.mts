/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace TaskTargets {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `TaskTargets`
 */
export declare class TaskTargets {
    protected readonly _options: TaskTargets.Options;
    constructor(_options: TaskTargets.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **taskTargets**
     *
     * @param {HomerApi.FindManyTaskTargetsRequest} request
     * @param {TaskTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.taskTargets.findManyTaskTargets()
     */
    findManyTaskTargets(request?: HomerApi.FindManyTaskTargetsRequest, requestOptions?: TaskTargets.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyTaskTargetsResponse>;
    private __findManyTaskTargets;
    /**
     * @param {HomerApi.CreateOneTaskTargetRequest} request
     * @param {TaskTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.taskTargets.createOneTaskTarget({
     *         body: {}
     *     })
     */
    createOneTaskTarget(request: HomerApi.CreateOneTaskTargetRequest, requestOptions?: TaskTargets.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneTaskTargetResponse>;
    private __createOneTaskTarget;
    /**
     * @param {HomerApi.CreateManyTaskTargetsRequest} request
     * @param {TaskTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.taskTargets.createManyTaskTargets({
     *         body: [{}]
     *     })
     */
    createManyTaskTargets(request: HomerApi.CreateManyTaskTargetsRequest, requestOptions?: TaskTargets.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyTaskTargetsResponse>;
    private __createManyTaskTargets;
    /**
     * **depth** can be provided to request your **taskTarget**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneTaskTargetRequest} request
     * @param {TaskTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.taskTargets.findOneTaskTarget("id")
     */
    findOneTaskTarget(id: string, request?: HomerApi.FindOneTaskTargetRequest, requestOptions?: TaskTargets.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneTaskTargetResponse>;
    private __findOneTaskTarget;
    /**
     * @param {string} id - Object id.
     * @param {TaskTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.taskTargets.deleteOneTaskTarget("id")
     */
    deleteOneTaskTarget(id: string, requestOptions?: TaskTargets.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneTaskTargetResponse>;
    private __deleteOneTaskTarget;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.TaskTargetForUpdate} request
     * @param {TaskTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.taskTargets.updateOneTaskTarget("id")
     */
    updateOneTaskTarget(id: string, request?: HomerApi.TaskTargetForUpdate, requestOptions?: TaskTargets.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneTaskTargetResponse>;
    private __updateOneTaskTarget;
    /**
     * **depth** can be provided to request your **taskTarget**
     *
     * @param {HomerApi.FindTaskTargetDuplicatesRequest} request
     * @param {TaskTargets.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.taskTargets.findTaskTargetDuplicates()
     */
    findTaskTargetDuplicates(request?: HomerApi.FindTaskTargetDuplicatesRequest, requestOptions?: TaskTargets.RequestOptions): core.HttpResponsePromise<HomerApi.FindTaskTargetDuplicatesResponse>;
    private __findTaskTargetDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
