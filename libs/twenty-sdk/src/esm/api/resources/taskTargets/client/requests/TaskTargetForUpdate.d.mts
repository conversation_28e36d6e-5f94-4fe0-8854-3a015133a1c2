/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface TaskTargetForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** TaskTarget task id foreign key */
    taskId?: string;
    /** TaskTarget person id foreign key */
    personId?: string;
    /** TaskTarget company id foreign key */
    companyId?: string;
    /** TaskTarget opportunity id foreign key */
    opportunityId?: string;
    /** TaskTarget property id foreign key */
    propertyId?: string;
}
