/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyTaskTargetsResponse {
    data?: FindManyTaskTargetsResponse.Data;
    pageInfo?: FindManyTaskTargetsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyTaskTargetsResponse {
    interface Data {
        taskTargets?: HomerApi.TaskTargetForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
