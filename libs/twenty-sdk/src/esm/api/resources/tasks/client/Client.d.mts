/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace Tasks {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Tasks`
 */
export declare class Tasks {
    protected readonly _options: Tasks.Options;
    constructor(_options: Tasks.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **tasks**
     *
     * @param {HomerApi.FindManyTasksRequest} request
     * @param {Tasks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.tasks.findManyTasks()
     */
    findManyTasks(request?: HomerApi.FindManyTasksRequest, requestOptions?: Tasks.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyTasksResponse>;
    private __findManyTasks;
    /**
     * @param {HomerApi.CreateOneTaskRequest} request
     * @param {Tasks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.tasks.createOneTask({
     *         body: {}
     *     })
     */
    createOneTask(request: HomerApi.CreateOneTaskRequest, requestOptions?: Tasks.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneTaskResponse>;
    private __createOneTask;
    /**
     * @param {HomerApi.CreateManyTasksRequest} request
     * @param {Tasks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.tasks.createManyTasks({
     *         body: [{}]
     *     })
     */
    createManyTasks(request: HomerApi.CreateManyTasksRequest, requestOptions?: Tasks.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyTasksResponse>;
    private __createManyTasks;
    /**
     * **depth** can be provided to request your **task**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneTaskRequest} request
     * @param {Tasks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.tasks.findOneTask("id")
     */
    findOneTask(id: string, request?: HomerApi.FindOneTaskRequest, requestOptions?: Tasks.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneTaskResponse>;
    private __findOneTask;
    /**
     * @param {string} id - Object id.
     * @param {Tasks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.tasks.deleteOneTask("id")
     */
    deleteOneTask(id: string, requestOptions?: Tasks.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneTaskResponse>;
    private __deleteOneTask;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.TaskForUpdate} request
     * @param {Tasks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.tasks.updateOneTask("id")
     */
    updateOneTask(id: string, request?: HomerApi.TaskForUpdate, requestOptions?: Tasks.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneTaskResponse>;
    private __updateOneTask;
    /**
     * **depth** can be provided to request your **task**
     *
     * @param {HomerApi.FindTaskDuplicatesRequest} request
     * @param {Tasks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.tasks.findTaskDuplicates()
     */
    findTaskDuplicates(request?: HomerApi.FindTaskDuplicatesRequest, requestOptions?: Tasks.RequestOptions): core.HttpResponsePromise<HomerApi.FindTaskDuplicatesResponse>;
    private __findTaskDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
