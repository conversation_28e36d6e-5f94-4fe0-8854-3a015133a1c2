/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface TaskForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Task record position */
    position?: number;
    /** Task title */
    title?: string;
    /** Task body */
    body?: string;
    /** Task body */
    bodyV2?: TaskForUpdate.BodyV2;
    /** Task due date */
    dueAt?: string;
    /** Task status */
    status?: TaskForUpdate.Status;
    /** The creator of the record */
    createdBy?: TaskForUpdate.CreatedBy;
    /** Task type */
    taskType?: TaskForUpdate.TaskType;
    /** Task assignee id foreign key */
    assigneeId?: string;
}
export declare namespace TaskForUpdate {
    /**
     * Task body
     */
    interface BodyV2 {
        blocknote?: string;
        markdown?: string;
    }
    /**
     * Task status
     */
    type Status = "TODO" | "IN_PROGRESS" | "DONE";
    const Status: {
        readonly Todo: "TODO";
        readonly InProgress: "IN_PROGRESS";
        readonly Done: "DONE";
    };
    /**
     * The creator of the record
     */
    interface CreatedBy {
        source?: CreatedBy.Source;
    }
    namespace CreatedBy {
        type Source = "EMAIL" | "CALENDAR" | "WORKFLOW" | "API" | "IMPORT" | "MANUAL" | "SYSTEM";
        const Source: {
            readonly Email: "EMAIL";
            readonly Calendar: "CALENDAR";
            readonly Workflow: "WORKFLOW";
            readonly Api: "API";
            readonly Import: "IMPORT";
            readonly Manual: "MANUAL";
            readonly System: "SYSTEM";
        };
    }
    /**
     * Task type
     */
    type TaskType = "NONE" | "PROACTIVE_MESSAGE" | "PHONE_CALL";
    const TaskType: {
        readonly None: "NONE";
        readonly ProactiveMessage: "PROACTIVE_MESSAGE";
        readonly PhoneCall: "PHONE_CALL";
    };
}
