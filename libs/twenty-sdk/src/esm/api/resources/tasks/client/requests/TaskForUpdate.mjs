/**
 * This file was auto-generated by Fe<PERSON> from our API Definition.
 */
export var TaskForUpdate;
(function (TaskForUpdate) {
    TaskForUpdate.Status = {
        Todo: "TODO",
        InProgress: "IN_PROGRESS",
        Done: "DONE",
    };
    let CreatedBy;
    (function (CreatedBy) {
        CreatedBy.Source = {
            Email: "EMAIL",
            Calendar: "CALENDAR",
            Workflow: "WORKFLOW",
            Api: "API",
            Import: "IMPORT",
            Manual: "MANUAL",
            System: "SYSTEM",
        };
    })(CreatedBy = TaskForUpdate.CreatedBy || (TaskForUpdate.CreatedBy = {}));
    TaskForUpdate.TaskType = {
        None: "NONE",
        ProactiveMessage: "PROACTIVE_MESSAGE",
        PhoneCall: "PHONE_CALL",
    };
})(TaskForUpdate || (TaskForUpdate = {}));
