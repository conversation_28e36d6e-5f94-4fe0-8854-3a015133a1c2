/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyTasksResponse {
    data?: FindManyTasksResponse.Data;
    pageInfo?: FindManyTasksResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyTasksResponse {
    interface Data {
        tasks?: HomerApi.TaskForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
