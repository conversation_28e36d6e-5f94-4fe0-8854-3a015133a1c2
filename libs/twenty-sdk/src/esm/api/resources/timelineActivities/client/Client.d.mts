/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as HomerApi from "../../../index.mjs";
export declare namespace TimelineActivities {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `TimelineActivities`
 */
export declare class TimelineActivities {
    protected readonly _options: TimelineActivities.Options;
    constructor(_options: TimelineActivities.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **timelineActivities**
     *
     * @param {HomerApi.FindManyTimelineActivitiesRequest} request
     * @param {TimelineActivities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.timelineActivities.findManyTimelineActivities()
     */
    findManyTimelineActivities(request?: HomerApi.FindManyTimelineActivitiesRequest, requestOptions?: TimelineActivities.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyTimelineActivitiesResponse>;
    private __findManyTimelineActivities;
    /**
     * @param {HomerApi.CreateOneTimelineActivityRequest} request
     * @param {TimelineActivities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.timelineActivities.createOneTimelineActivity({
     *         body: {}
     *     })
     */
    createOneTimelineActivity(request: HomerApi.CreateOneTimelineActivityRequest, requestOptions?: TimelineActivities.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneTimelineActivityResponse>;
    private __createOneTimelineActivity;
    /**
     * @param {HomerApi.CreateManyTimelineActivitiesRequest} request
     * @param {TimelineActivities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.timelineActivities.createManyTimelineActivities({
     *         body: [{}]
     *     })
     */
    createManyTimelineActivities(request: HomerApi.CreateManyTimelineActivitiesRequest, requestOptions?: TimelineActivities.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyTimelineActivitiesResponse>;
    private __createManyTimelineActivities;
    /**
     * **depth** can be provided to request your **timelineActivity**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneTimelineActivityRequest} request
     * @param {TimelineActivities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.timelineActivities.findOneTimelineActivity("id")
     */
    findOneTimelineActivity(id: string, request?: HomerApi.FindOneTimelineActivityRequest, requestOptions?: TimelineActivities.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneTimelineActivityResponse>;
    private __findOneTimelineActivity;
    /**
     * @param {string} id - Object id.
     * @param {TimelineActivities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.timelineActivities.deleteOneTimelineActivity("id")
     */
    deleteOneTimelineActivity(id: string, requestOptions?: TimelineActivities.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneTimelineActivityResponse>;
    private __deleteOneTimelineActivity;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.TimelineActivityForUpdate} request
     * @param {TimelineActivities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.timelineActivities.updateOneTimelineActivity("id")
     */
    updateOneTimelineActivity(id: string, request?: HomerApi.TimelineActivityForUpdate, requestOptions?: TimelineActivities.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneTimelineActivityResponse>;
    private __updateOneTimelineActivity;
    /**
     * **depth** can be provided to request your **timelineActivity**
     *
     * @param {HomerApi.FindTimelineActivityDuplicatesRequest} request
     * @param {TimelineActivities.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.timelineActivities.findTimelineActivityDuplicates()
     */
    findTimelineActivityDuplicates(request?: HomerApi.FindTimelineActivityDuplicatesRequest, requestOptions?: TimelineActivities.RequestOptions): core.HttpResponsePromise<HomerApi.FindTimelineActivityDuplicatesResponse>;
    private __findTimelineActivityDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
