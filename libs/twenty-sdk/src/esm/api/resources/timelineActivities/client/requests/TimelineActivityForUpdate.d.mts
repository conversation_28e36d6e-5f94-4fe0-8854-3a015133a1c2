/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface TimelineActivityForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Creation date */
    happensAt?: string;
    /** Event name */
    name?: string;
    /** Json value for event details */
    properties?: Record<string, unknown>;
    /** Cached record name */
    linkedRecordCachedName?: string;
    /** Linked Record id */
    linkedRecordId?: string;
    /** Linked Object Metadata Id */
    linkedObjectMetadataId?: string;
    /** Event workspace member id foreign key */
    workspaceMemberId?: string;
    /** Event person id foreign key */
    personId?: string;
    /** Event company id foreign key */
    companyId?: string;
    /** Event opportunity id foreign key */
    opportunityId?: string;
    /** Event note id foreign key */
    noteId?: string;
    /** Event task id foreign key */
    taskId?: string;
    /** Event workflow id foreign key */
    workflowId?: string;
    /** Event workflow version id foreign key */
    workflowVersionId?: string;
    /** Event workflow run id foreign key */
    workflowRunId?: string;
    /** Event property id foreign key */
    propertyId?: string;
}
