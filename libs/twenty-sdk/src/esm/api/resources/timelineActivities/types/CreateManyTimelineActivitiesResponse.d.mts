/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface CreateManyTimelineActivitiesResponse {
    data?: CreateManyTimelineActivitiesResponse.Data;
}
export declare namespace CreateManyTimelineActivitiesResponse {
    interface Data {
        createTimelineActivities?: HomerApi.TimelineActivityForResponse[];
    }
}
