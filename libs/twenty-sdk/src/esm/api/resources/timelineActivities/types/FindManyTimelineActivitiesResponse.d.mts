/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyTimelineActivitiesResponse {
    data?: FindManyTimelineActivitiesResponse.Data;
    pageInfo?: FindManyTimelineActivitiesResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyTimelineActivitiesResponse {
    interface Data {
        timelineActivities?: HomerApi.TimelineActivityForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
