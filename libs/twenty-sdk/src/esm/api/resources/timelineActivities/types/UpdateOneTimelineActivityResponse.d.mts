/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface UpdateOneTimelineActivityResponse {
    data?: UpdateOneTimelineActivityResponse.Data;
}
export declare namespace UpdateOneTimelineActivityResponse {
    interface Data {
        updateTimelineActivity?: HomerApi.TimelineActivityForResponse;
    }
}
