/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace ViewFields {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `ViewFields`
 */
export declare class ViewFields {
    protected readonly _options: ViewFields.Options;
    constructor(_options: ViewFields.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **viewFields**
     *
     * @param {HomerApi.FindManyViewFieldsRequest} request
     * @param {ViewFields.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFields.findManyViewFields()
     */
    findManyViewFields(request?: HomerApi.FindManyViewFieldsRequest, requestOptions?: ViewFields.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyViewFieldsResponse>;
    private __findManyViewFields;
    /**
     * @param {HomerApi.CreateOneViewFieldRequest} request
     * @param {ViewFields.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFields.createOneViewField({
     *         body: {
     *             fieldMetadataId: "fieldMetadataId",
     *             viewId: "viewId"
     *         }
     *     })
     */
    createOneViewField(request: HomerApi.CreateOneViewFieldRequest, requestOptions?: ViewFields.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneViewFieldResponse>;
    private __createOneViewField;
    /**
     * @param {HomerApi.CreateManyViewFieldsRequest} request
     * @param {ViewFields.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFields.createManyViewFields({
     *         body: [{
     *                 fieldMetadataId: "fieldMetadataId",
     *                 viewId: "viewId"
     *             }]
     *     })
     */
    createManyViewFields(request: HomerApi.CreateManyViewFieldsRequest, requestOptions?: ViewFields.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyViewFieldsResponse>;
    private __createManyViewFields;
    /**
     * **depth** can be provided to request your **viewField**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneViewFieldRequest} request
     * @param {ViewFields.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFields.findOneViewField("id")
     */
    findOneViewField(id: string, request?: HomerApi.FindOneViewFieldRequest, requestOptions?: ViewFields.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneViewFieldResponse>;
    private __findOneViewField;
    /**
     * @param {string} id - Object id.
     * @param {ViewFields.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFields.deleteOneViewField("id")
     */
    deleteOneViewField(id: string, requestOptions?: ViewFields.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneViewFieldResponse>;
    private __deleteOneViewField;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.ViewFieldForUpdate} request
     * @param {ViewFields.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFields.updateOneViewField("id")
     */
    updateOneViewField(id: string, request?: HomerApi.ViewFieldForUpdate, requestOptions?: ViewFields.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneViewFieldResponse>;
    private __updateOneViewField;
    /**
     * **depth** can be provided to request your **viewField**
     *
     * @param {HomerApi.FindViewFieldDuplicatesRequest} request
     * @param {ViewFields.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFields.findViewFieldDuplicates()
     */
    findViewFieldDuplicates(request?: HomerApi.FindViewFieldDuplicatesRequest, requestOptions?: ViewFields.RequestOptions): core.HttpResponsePromise<HomerApi.FindViewFieldDuplicatesResponse>;
    private __findViewFieldDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
