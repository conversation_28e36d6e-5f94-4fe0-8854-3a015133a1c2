/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface ViewFieldForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** View Field target field */
    fieldMetadataId?: string;
    /** View Field visibility */
    isVisible?: boolean;
    /** View Field size */
    size?: number;
    /** View Field position */
    position?: number;
    /** Optional aggregate operation */
    aggregateOperation?: ViewFieldForUpdate.AggregateOperation;
    /** View Field related view id foreign key */
    viewId?: string;
}
export declare namespace ViewFieldForUpdate {
    /**
     * Optional aggregate operation
     */
    type AggregateOperation = "AVG" | "COUNT" | "MAX" | "MIN" | "SUM" | "COUNT_EMPTY" | "COUNT_NOT_EMPTY" | "COUNT_UNIQUE_VALUES" | "PERCENTAGE_EMPTY" | "PERCENTAGE_NOT_EMPTY";
    const AggregateOperation: {
        readonly Avg: "AVG";
        readonly Count: "COUNT";
        readonly Max: "MAX";
        readonly Min: "MIN";
        readonly Sum: "SUM";
        readonly CountEmpty: "COUNT_EMPTY";
        readonly CountNotEmpty: "COUNT_NOT_EMPTY";
        readonly CountUniqueValues: "COUNT_UNIQUE_VALUES";
        readonly PercentageEmpty: "PERCENTAGE_EMPTY";
        readonly PercentageNotEmpty: "PERCENTAGE_NOT_EMPTY";
    };
}
