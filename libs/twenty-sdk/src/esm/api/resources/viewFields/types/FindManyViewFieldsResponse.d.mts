/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyViewFieldsResponse {
    data?: FindManyViewFieldsResponse.Data;
    pageInfo?: FindManyViewFieldsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyViewFieldsResponse {
    interface Data {
        viewFields?: HomerApi.ViewFieldForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
