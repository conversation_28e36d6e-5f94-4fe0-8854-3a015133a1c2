/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON>pi from "../../../index.mjs";
export declare namespace ViewFilterGroups {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `ViewFilterGroups`
 */
export declare class ViewFilterGroups {
    protected readonly _options: ViewFilterGroups.Options;
    constructor(_options: ViewFilterGroups.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **viewFilterGroups**
     *
     * @param {HomerApi.FindManyViewFilterGroupsRequest} request
     * @param {ViewFilterGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilterGroups.findManyViewFilterGroups()
     */
    findManyViewFilterGroups(request?: HomerApi.FindManyViewFilterGroupsRequest, requestOptions?: ViewFilterGroups.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyViewFilterGroupsResponse>;
    private __findManyViewFilterGroups;
    /**
     * @param {HomerApi.CreateOneViewFilterGroupRequest} request
     * @param {ViewFilterGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilterGroups.createOneViewFilterGroup({
     *         body: {
     *             viewId: "viewId"
     *         }
     *     })
     */
    createOneViewFilterGroup(request: HomerApi.CreateOneViewFilterGroupRequest, requestOptions?: ViewFilterGroups.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneViewFilterGroupResponse>;
    private __createOneViewFilterGroup;
    /**
     * @param {HomerApi.CreateManyViewFilterGroupsRequest} request
     * @param {ViewFilterGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilterGroups.createManyViewFilterGroups({
     *         body: [{
     *                 viewId: "viewId"
     *             }]
     *     })
     */
    createManyViewFilterGroups(request: HomerApi.CreateManyViewFilterGroupsRequest, requestOptions?: ViewFilterGroups.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyViewFilterGroupsResponse>;
    private __createManyViewFilterGroups;
    /**
     * **depth** can be provided to request your **viewFilterGroup**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneViewFilterGroupRequest} request
     * @param {ViewFilterGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilterGroups.findOneViewFilterGroup("id")
     */
    findOneViewFilterGroup(id: string, request?: HomerApi.FindOneViewFilterGroupRequest, requestOptions?: ViewFilterGroups.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneViewFilterGroupResponse>;
    private __findOneViewFilterGroup;
    /**
     * @param {string} id - Object id.
     * @param {ViewFilterGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilterGroups.deleteOneViewFilterGroup("id")
     */
    deleteOneViewFilterGroup(id: string, requestOptions?: ViewFilterGroups.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneViewFilterGroupResponse>;
    private __deleteOneViewFilterGroup;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.ViewFilterGroupForUpdate} request
     * @param {ViewFilterGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilterGroups.updateOneViewFilterGroup("id")
     */
    updateOneViewFilterGroup(id: string, request?: HomerApi.ViewFilterGroupForUpdate, requestOptions?: ViewFilterGroups.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneViewFilterGroupResponse>;
    private __updateOneViewFilterGroup;
    /**
     * **depth** can be provided to request your **viewFilterGroup**
     *
     * @param {HomerApi.FindViewFilterGroupDuplicatesRequest} request
     * @param {ViewFilterGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilterGroups.findViewFilterGroupDuplicates()
     */
    findViewFilterGroupDuplicates(request?: HomerApi.FindViewFilterGroupDuplicatesRequest, requestOptions?: ViewFilterGroups.RequestOptions): core.HttpResponsePromise<HomerApi.FindViewFilterGroupDuplicatesResponse>;
    private __findViewFilterGroupDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
