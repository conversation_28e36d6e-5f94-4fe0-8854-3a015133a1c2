/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface CreateManyViewFilterGroupsResponse {
    data?: CreateManyViewFilterGroupsResponse.Data;
}
export declare namespace CreateManyViewFilterGroupsResponse {
    interface Data {
        createViewFilterGroups?: HomerApi.ViewFilterGroupForResponse[];
    }
}
