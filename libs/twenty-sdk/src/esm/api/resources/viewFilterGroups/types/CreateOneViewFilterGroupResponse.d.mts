/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface CreateOneViewFilterGroupResponse {
    data?: CreateOneViewFilterGroupResponse.Data;
}
export declare namespace CreateOneViewFilterGroupResponse {
    interface Data {
        createViewFilterGroup?: HomerApi.ViewFilterGroupForResponse;
    }
}
