/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface DeleteOneViewFilterGroupResponse {
    data?: DeleteOneViewFilterGroupResponse.Data;
}
export declare namespace DeleteOneViewFilterGroupResponse {
    interface Data {
        deleteViewFilterGroup?: Data.DeleteViewFilterGroup;
    }
    namespace Data {
        interface DeleteViewFilterGroup {
            id?: string;
        }
    }
}
