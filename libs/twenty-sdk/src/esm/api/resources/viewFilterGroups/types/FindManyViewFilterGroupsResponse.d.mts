/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyViewFilterGroupsResponse {
    data?: FindManyViewFilterGroupsResponse.Data;
    pageInfo?: FindManyViewFilterGroupsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyViewFilterGroupsResponse {
    interface Data {
        viewFilterGroups?: HomerApi.ViewFilterGroupForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
