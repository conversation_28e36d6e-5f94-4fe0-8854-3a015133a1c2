/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface UpdateOneViewFilterGroupResponse {
    data?: UpdateOneViewFilterGroupResponse.Data;
}
export declare namespace UpdateOneViewFilterGroupResponse {
    interface Data {
        updateViewFilterGroup?: HomerApi.ViewFilterGroupForResponse;
    }
}
