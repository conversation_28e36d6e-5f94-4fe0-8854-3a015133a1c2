/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON>pi from "../../../index.mjs";
export declare namespace ViewFilters {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `ViewFilters`
 */
export declare class ViewFilters {
    protected readonly _options: ViewFilters.Options;
    constructor(_options: ViewFilters.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **viewFilters**
     *
     * @param {HomerApi.FindManyViewFiltersRequest} request
     * @param {ViewFilters.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilters.findManyViewFilters()
     */
    findManyViewFilters(request?: HomerApi.FindManyViewFiltersRequest, requestOptions?: ViewFilters.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyViewFiltersResponse>;
    private __findManyViewFilters;
    /**
     * @param {HomerApi.CreateOneViewFilterRequest} request
     * @param {ViewFilters.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilters.createOneViewFilter({
     *         body: {
     *             fieldMetadataId: "fieldMetadataId"
     *         }
     *     })
     */
    createOneViewFilter(request: HomerApi.CreateOneViewFilterRequest, requestOptions?: ViewFilters.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneViewFilterResponse>;
    private __createOneViewFilter;
    /**
     * @param {HomerApi.CreateManyViewFiltersRequest} request
     * @param {ViewFilters.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilters.createManyViewFilters({
     *         body: [{
     *                 fieldMetadataId: "fieldMetadataId"
     *             }]
     *     })
     */
    createManyViewFilters(request: HomerApi.CreateManyViewFiltersRequest, requestOptions?: ViewFilters.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyViewFiltersResponse>;
    private __createManyViewFilters;
    /**
     * **depth** can be provided to request your **viewFilter**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneViewFilterRequest} request
     * @param {ViewFilters.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilters.findOneViewFilter("id")
     */
    findOneViewFilter(id: string, request?: HomerApi.FindOneViewFilterRequest, requestOptions?: ViewFilters.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneViewFilterResponse>;
    private __findOneViewFilter;
    /**
     * @param {string} id - Object id.
     * @param {ViewFilters.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilters.deleteOneViewFilter("id")
     */
    deleteOneViewFilter(id: string, requestOptions?: ViewFilters.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneViewFilterResponse>;
    private __deleteOneViewFilter;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.ViewFilterForUpdate} request
     * @param {ViewFilters.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilters.updateOneViewFilter("id")
     */
    updateOneViewFilter(id: string, request?: HomerApi.ViewFilterForUpdate, requestOptions?: ViewFilters.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneViewFilterResponse>;
    private __updateOneViewFilter;
    /**
     * **depth** can be provided to request your **viewFilter**
     *
     * @param {HomerApi.FindViewFilterDuplicatesRequest} request
     * @param {ViewFilters.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewFilters.findViewFilterDuplicates()
     */
    findViewFilterDuplicates(request?: HomerApi.FindViewFilterDuplicatesRequest, requestOptions?: ViewFilters.RequestOptions): core.HttpResponsePromise<HomerApi.FindViewFilterDuplicatesResponse>;
    private __findViewFilterDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
