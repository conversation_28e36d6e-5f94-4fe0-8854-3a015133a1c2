/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyViewFiltersResponse {
    data?: FindManyViewFiltersResponse.Data;
    pageInfo?: FindManyViewFiltersResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyViewFiltersResponse {
    interface Data {
        viewFilters?: HomerApi.ViewFilterForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
