/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace ViewGroups {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `ViewGroups`
 */
export declare class ViewGroups {
    protected readonly _options: ViewGroups.Options;
    constructor(_options: ViewGroups.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **viewGroups**
     *
     * @param {HomerApi.FindManyViewGroupsRequest} request
     * @param {ViewGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewGroups.findManyViewGroups()
     */
    findManyViewGroups(request?: HomerApi.FindManyViewGroupsRequest, requestOptions?: ViewGroups.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyViewGroupsResponse>;
    private __findManyViewGroups;
    /**
     * @param {HomerApi.CreateOneViewGroupRequest} request
     * @param {ViewGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewGroups.createOneViewGroup({
     *         body: {
     *             fieldMetadataId: "fieldMetadataId"
     *         }
     *     })
     */
    createOneViewGroup(request: HomerApi.CreateOneViewGroupRequest, requestOptions?: ViewGroups.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneViewGroupResponse>;
    private __createOneViewGroup;
    /**
     * @param {HomerApi.CreateManyViewGroupsRequest} request
     * @param {ViewGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewGroups.createManyViewGroups({
     *         body: [{
     *                 fieldMetadataId: "fieldMetadataId"
     *             }]
     *     })
     */
    createManyViewGroups(request: HomerApi.CreateManyViewGroupsRequest, requestOptions?: ViewGroups.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyViewGroupsResponse>;
    private __createManyViewGroups;
    /**
     * **depth** can be provided to request your **viewGroup**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneViewGroupRequest} request
     * @param {ViewGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewGroups.findOneViewGroup("id")
     */
    findOneViewGroup(id: string, request?: HomerApi.FindOneViewGroupRequest, requestOptions?: ViewGroups.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneViewGroupResponse>;
    private __findOneViewGroup;
    /**
     * @param {string} id - Object id.
     * @param {ViewGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewGroups.deleteOneViewGroup("id")
     */
    deleteOneViewGroup(id: string, requestOptions?: ViewGroups.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneViewGroupResponse>;
    private __deleteOneViewGroup;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.ViewGroupForUpdate} request
     * @param {ViewGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewGroups.updateOneViewGroup("id")
     */
    updateOneViewGroup(id: string, request?: HomerApi.ViewGroupForUpdate, requestOptions?: ViewGroups.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneViewGroupResponse>;
    private __updateOneViewGroup;
    /**
     * **depth** can be provided to request your **viewGroup**
     *
     * @param {HomerApi.FindViewGroupDuplicatesRequest} request
     * @param {ViewGroups.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewGroups.findViewGroupDuplicates()
     */
    findViewGroupDuplicates(request?: HomerApi.FindViewGroupDuplicatesRequest, requestOptions?: ViewGroups.RequestOptions): core.HttpResponsePromise<HomerApi.FindViewGroupDuplicatesResponse>;
    private __findViewGroupDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
