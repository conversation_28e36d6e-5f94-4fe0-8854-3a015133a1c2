/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as <PERSON><PERSON><PERSON> from "../../../index.mjs";
export interface FindManyViewGroupsResponse {
    data?: FindManyViewGroupsResponse.Data;
    pageInfo?: FindManyViewGroupsResponse.PageInfo;
    totalCount?: number;
}
export declare namespace FindManyViewGroupsResponse {
    interface Data {
        viewGroups?: HomerApi.ViewGroupForResponse[];
    }
    interface PageInfo {
        hasNextPage?: boolean;
        startCursor?: string;
        endCursor?: string;
    }
}
