/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace ViewSorts {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `ViewSorts`
 */
export declare class ViewSorts {
    protected readonly _options: ViewSorts.Options;
    constructor(_options: ViewSorts.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **viewSorts**
     *
     * @param {HomerApi.FindManyViewSortsRequest} request
     * @param {ViewSorts.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewSorts.findManyViewSorts()
     */
    findManyViewSorts(request?: HomerApi.FindManyViewSortsRequest, requestOptions?: ViewSorts.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyViewSortsResponse>;
    private __findManyViewSorts;
    /**
     * @param {HomerApi.CreateOneViewSortRequest} request
     * @param {ViewSorts.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewSorts.createOneViewSort({
     *         body: {
     *             fieldMetadataId: "fieldMetadataId"
     *         }
     *     })
     */
    createOneViewSort(request: HomerApi.CreateOneViewSortRequest, requestOptions?: ViewSorts.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneViewSortResponse>;
    private __createOneViewSort;
    /**
     * @param {HomerApi.CreateManyViewSortsRequest} request
     * @param {ViewSorts.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewSorts.createManyViewSorts({
     *         body: [{
     *                 fieldMetadataId: "fieldMetadataId"
     *             }]
     *     })
     */
    createManyViewSorts(request: HomerApi.CreateManyViewSortsRequest, requestOptions?: ViewSorts.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyViewSortsResponse>;
    private __createManyViewSorts;
    /**
     * **depth** can be provided to request your **viewSort**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneViewSortRequest} request
     * @param {ViewSorts.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewSorts.findOneViewSort("id")
     */
    findOneViewSort(id: string, request?: HomerApi.FindOneViewSortRequest, requestOptions?: ViewSorts.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneViewSortResponse>;
    private __findOneViewSort;
    /**
     * @param {string} id - Object id.
     * @param {ViewSorts.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewSorts.deleteOneViewSort("id")
     */
    deleteOneViewSort(id: string, requestOptions?: ViewSorts.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneViewSortResponse>;
    private __deleteOneViewSort;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.ViewSortForUpdate} request
     * @param {ViewSorts.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewSorts.updateOneViewSort("id")
     */
    updateOneViewSort(id: string, request?: HomerApi.ViewSortForUpdate, requestOptions?: ViewSorts.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneViewSortResponse>;
    private __updateOneViewSort;
    /**
     * **depth** can be provided to request your **viewSort**
     *
     * @param {HomerApi.FindViewSortDuplicatesRequest} request
     * @param {ViewSorts.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.viewSorts.findViewSortDuplicates()
     */
    findViewSortDuplicates(request?: HomerApi.FindViewSortDuplicatesRequest, requestOptions?: ViewSorts.RequestOptions): core.HttpResponsePromise<HomerApi.FindViewSortDuplicatesResponse>;
    private __findViewSortDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
