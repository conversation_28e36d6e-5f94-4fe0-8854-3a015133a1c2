/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace Views {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Views`
 */
export declare class Views {
    protected readonly _options: Views.Options;
    constructor(_options: Views.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **views**
     *
     * @param {HomerApi.FindManyViewsRequest} request
     * @param {Views.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.views.findManyViews()
     */
    findManyViews(request?: HomerApi.FindManyViewsRequest, requestOptions?: Views.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyViewsResponse>;
    private __findManyViews;
    /**
     * @param {HomerApi.CreateOneViewRequest} request
     * @param {Views.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.views.createOneView({
     *         body: {
     *             objectMetadataId: "objectMetadataId"
     *         }
     *     })
     */
    createOneView(request: HomerApi.CreateOneViewRequest, requestOptions?: Views.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneViewResponse>;
    private __createOneView;
    /**
     * @param {HomerApi.CreateManyViewsRequest} request
     * @param {Views.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.views.createManyViews({
     *         body: [{
     *                 objectMetadataId: "objectMetadataId"
     *             }]
     *     })
     */
    createManyViews(request: HomerApi.CreateManyViewsRequest, requestOptions?: Views.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyViewsResponse>;
    private __createManyViews;
    /**
     * **depth** can be provided to request your **view**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneViewRequest} request
     * @param {Views.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.views.findOneView("id")
     */
    findOneView(id: string, request?: HomerApi.FindOneViewRequest, requestOptions?: Views.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneViewResponse>;
    private __findOneView;
    /**
     * @param {string} id - Object id.
     * @param {Views.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.views.deleteOneView("id")
     */
    deleteOneView(id: string, requestOptions?: Views.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneViewResponse>;
    private __deleteOneView;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.ViewForUpdate} request
     * @param {Views.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.views.updateOneView("id")
     */
    updateOneView(id: string, request?: HomerApi.ViewForUpdate, requestOptions?: Views.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneViewResponse>;
    private __updateOneView;
    /**
     * **depth** can be provided to request your **view**
     *
     * @param {HomerApi.FindViewDuplicatesRequest} request
     * @param {Views.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.views.findViewDuplicates()
     */
    findViewDuplicates(request?: HomerApi.FindViewDuplicatesRequest, requestOptions?: Views.RequestOptions): core.HttpResponsePromise<HomerApi.FindViewDuplicatesResponse>;
    private __findViewDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
