/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface FindManyViewsRequest {
    /**
     * Sorts objects returned.
     *     Should have the following shape: **field_name_1,field_name_2[DIRECTION_2],...**
     *     Available directions are **AscNullsFirst**, **AscNullsLast**, **DescNullsFirst**, **DescNullsLast**.
     *     Default direction is **AscNullsFirst**
     */
    order_by?: string;
    /**
     * Filters objects returned.
     *     Should have the following shape: **field_1[COMPARATOR]:value_1,field_2[COMPARATOR]:value_2...
     *     To filter on composite type fields use **field.subField[COMPARATOR]:value_1
     *     **
     *     Available comparators are **eq**, **neq**, **in**, **containsAny**, **is**, **gt**, **gte**, **lt**, **lte**, **startsWith**, **like**, **ilike**.
     *     You can create more complex filters using conjunctions **or**, **and**, **not**.
     *     Default root conjunction is **and**.
     *     To filter **null** values use **field[is]:NULL** or **field[is]:NOT_NULL**
     *     To filter using **boolean** values use **field[eq]:true** or **field[eq]:false**
     */
    filter?: string;
    /**
     * Limits the number of objects returned.
     */
    limit?: number;
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /**
     * Returns objects starting after a specific cursor. You can find cursors in **startCursor** and **endCursor** in **pageInfo** in response data
     */
    starting_after?: string;
    /**
     * Returns objects ending before a specific cursor. You can find cursors in **startCursor** and **endCursor** in **pageInfo** in response data
     */
    ending_before?: string;
}
