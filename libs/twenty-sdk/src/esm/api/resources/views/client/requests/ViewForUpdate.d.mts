/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface ViewForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** View name */
    name?: string;
    /** View target object */
    objectMetadataId?: string;
    /** View type */
    type?: string;
    /** View key */
    key?: "INDEX";
    /** View icon */
    icon?: string;
    /** View Kanban column field */
    kanbanFieldMetadataId?: string;
    /** View position */
    position?: number;
    /** Describes if the view is in compact mode */
    isCompact?: boolean;
    /** Display the records in a side panel or in a record page */
    openRecordIn?: ViewForUpdate.OpenRecordIn;
    /** Optional aggregate operation */
    kanbanAggregateOperation?: ViewForUpdate.KanbanAggregateOperation;
    /** Field metadata used for aggregate operation */
    kanbanAggregateOperationFieldMetadataId?: string;
}
export declare namespace ViewForUpdate {
    /**
     * Display the records in a side panel or in a record page
     */
    type OpenRecordIn = "SIDE_PANEL" | "RECORD_PAGE";
    const OpenRecordIn: {
        readonly SidePanel: "SIDE_PANEL";
        readonly RecordPage: "RECORD_PAGE";
    };
    /**
     * Optional aggregate operation
     */
    type KanbanAggregateOperation = "AVG" | "COUNT" | "MAX" | "MIN" | "SUM" | "COUNT_EMPTY" | "COUNT_NOT_EMPTY" | "COUNT_UNIQUE_VALUES" | "PERCENTAGE_EMPTY" | "PERCENTAGE_NOT_EMPTY";
    const KanbanAggregateOperation: {
        readonly Avg: "AVG";
        readonly Count: "COUNT";
        readonly Max: "MAX";
        readonly Min: "MIN";
        readonly Sum: "SUM";
        readonly CountEmpty: "COUNT_EMPTY";
        readonly CountNotEmpty: "COUNT_NOT_EMPTY";
        readonly CountUniqueValues: "COUNT_UNIQUE_VALUES";
        readonly PercentageEmpty: "PERCENTAGE_EMPTY";
        readonly PercentageNotEmpty: "PERCENTAGE_NOT_EMPTY";
    };
}
