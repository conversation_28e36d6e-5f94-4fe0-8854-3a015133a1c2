/**
 * This file was auto-generated by Fern from our API Definition.
 */
export var ViewForUpdate;
(function (ViewForUpdate) {
    ViewForUpdate.OpenRecordIn = {
        SidePanel: "SIDE_PANEL",
        RecordPage: "RECORD_PAGE",
    };
    ViewForUpdate.KanbanAggregateOperation = {
        Avg: "AVG",
        Count: "COUNT",
        Max: "MAX",
        Min: "MIN",
        Sum: "SUM",
        CountEmpty: "COUNT_EMPTY",
        CountNotEmpty: "COUNT_NOT_EMPTY",
        CountUniqueValues: "COUNT_UNIQUE_VALUES",
        PercentageEmpty: "PERCENTAGE_EMPTY",
        PercentageNotEmpty: "PERCENTAGE_NOT_EMPTY",
    };
})(ViewForUpdate || (ViewForUpdate = {}));
