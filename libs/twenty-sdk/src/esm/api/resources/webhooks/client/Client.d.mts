/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace Webhooks {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Webhooks`
 */
export declare class Webhooks {
    protected readonly _options: Webhooks.Options;
    constructor(_options: Webhooks.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **webhooks**
     *
     * @param {HomerApi.FindManyWebhooksRequest} request
     * @param {Webhooks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.webhooks.findManyWebhooks()
     */
    findManyWebhooks(request?: HomerApi.FindManyWebhooksRequest, requestOptions?: Webhooks.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyWebhooksResponse>;
    private __findManyWebhooks;
    /**
     * @param {HomerApi.CreateOneWebhookRequest} request
     * @param {Webhooks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.webhooks.createOneWebhook({
     *         body: {}
     *     })
     */
    createOneWebhook(request: HomerApi.CreateOneWebhookRequest, requestOptions?: Webhooks.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneWebhookResponse>;
    private __createOneWebhook;
    /**
     * @param {HomerApi.CreateManyWebhooksRequest} request
     * @param {Webhooks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.webhooks.createManyWebhooks({
     *         body: [{}]
     *     })
     */
    createManyWebhooks(request: HomerApi.CreateManyWebhooksRequest, requestOptions?: Webhooks.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyWebhooksResponse>;
    private __createManyWebhooks;
    /**
     * **depth** can be provided to request your **webhook**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneWebhookRequest} request
     * @param {Webhooks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.webhooks.findOneWebhook("id")
     */
    findOneWebhook(id: string, request?: HomerApi.FindOneWebhookRequest, requestOptions?: Webhooks.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneWebhookResponse>;
    private __findOneWebhook;
    /**
     * @param {string} id - Object id.
     * @param {Webhooks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.webhooks.deleteOneWebhook("id")
     */
    deleteOneWebhook(id: string, requestOptions?: Webhooks.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneWebhookResponse>;
    private __deleteOneWebhook;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.WebhookForUpdate} request
     * @param {Webhooks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.webhooks.updateOneWebhook("id")
     */
    updateOneWebhook(id: string, request?: HomerApi.WebhookForUpdate, requestOptions?: Webhooks.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneWebhookResponse>;
    private __updateOneWebhook;
    /**
     * **depth** can be provided to request your **webhook**
     *
     * @param {HomerApi.FindWebhookDuplicatesRequest} request
     * @param {Webhooks.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.webhooks.findWebhookDuplicates()
     */
    findWebhookDuplicates(request?: HomerApi.FindWebhookDuplicatesRequest, requestOptions?: Webhooks.RequestOptions): core.HttpResponsePromise<HomerApi.FindWebhookDuplicatesResponse>;
    private __findWebhookDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
