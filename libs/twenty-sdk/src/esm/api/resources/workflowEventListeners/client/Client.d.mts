/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace WorkflowEventListeners {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `WorkflowEventListeners`
 */
export declare class WorkflowEventListeners {
    protected readonly _options: WorkflowEventListeners.Options;
    constructor(_options: WorkflowEventListeners.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **workflowEventListeners**
     *
     * @param {HomerApi.FindManyWorkflowEventListenersRequest} request
     * @param {WorkflowEventListeners.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowEventListeners.findManyWorkflowEventListeners()
     */
    findManyWorkflowEventListeners(request?: HomerApi.FindManyWorkflowEventListenersRequest, requestOptions?: WorkflowEventListeners.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyWorkflowEventListenersResponse>;
    private __findManyWorkflowEventListeners;
    /**
     * @param {HomerApi.CreateOneWorkflowEventListenerRequest} request
     * @param {WorkflowEventListeners.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowEventListeners.createOneWorkflowEventListener({
     *         body: {}
     *     })
     */
    createOneWorkflowEventListener(request: HomerApi.CreateOneWorkflowEventListenerRequest, requestOptions?: WorkflowEventListeners.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneWorkflowEventListenerResponse>;
    private __createOneWorkflowEventListener;
    /**
     * @param {HomerApi.CreateManyWorkflowEventListenersRequest} request
     * @param {WorkflowEventListeners.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowEventListeners.createManyWorkflowEventListeners({
     *         body: [{}]
     *     })
     */
    createManyWorkflowEventListeners(request: HomerApi.CreateManyWorkflowEventListenersRequest, requestOptions?: WorkflowEventListeners.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyWorkflowEventListenersResponse>;
    private __createManyWorkflowEventListeners;
    /**
     * **depth** can be provided to request your **workflowEventListener**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneWorkflowEventListenerRequest} request
     * @param {WorkflowEventListeners.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowEventListeners.findOneWorkflowEventListener("id")
     */
    findOneWorkflowEventListener(id: string, request?: HomerApi.FindOneWorkflowEventListenerRequest, requestOptions?: WorkflowEventListeners.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneWorkflowEventListenerResponse>;
    private __findOneWorkflowEventListener;
    /**
     * @param {string} id - Object id.
     * @param {WorkflowEventListeners.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowEventListeners.deleteOneWorkflowEventListener("id")
     */
    deleteOneWorkflowEventListener(id: string, requestOptions?: WorkflowEventListeners.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneWorkflowEventListenerResponse>;
    private __deleteOneWorkflowEventListener;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.WorkflowEventListenerForUpdate} request
     * @param {WorkflowEventListeners.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowEventListeners.updateOneWorkflowEventListener("id")
     */
    updateOneWorkflowEventListener(id: string, request?: HomerApi.WorkflowEventListenerForUpdate, requestOptions?: WorkflowEventListeners.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneWorkflowEventListenerResponse>;
    private __updateOneWorkflowEventListener;
    /**
     * **depth** can be provided to request your **workflowEventListener**
     *
     * @param {HomerApi.FindWorkflowEventListenerDuplicatesRequest} request
     * @param {WorkflowEventListeners.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowEventListeners.findWorkflowEventListenerDuplicates()
     */
    findWorkflowEventListenerDuplicates(request?: HomerApi.FindWorkflowEventListenerDuplicatesRequest, requestOptions?: WorkflowEventListeners.RequestOptions): core.HttpResponsePromise<HomerApi.FindWorkflowEventListenerDuplicatesResponse>;
    private __findWorkflowEventListenerDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
