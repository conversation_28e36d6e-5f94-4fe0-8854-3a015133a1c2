/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace WorkflowRuns {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `WorkflowRuns`
 */
export declare class WorkflowRuns {
    protected readonly _options: WorkflowRuns.Options;
    constructor(_options: WorkflowRuns.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **workflowRuns**
     *
     * @param {HomerApi.FindManyWorkflowRunsRequest} request
     * @param {WorkflowRuns.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowRuns.findManyWorkflowRuns()
     */
    findManyWorkflowRuns(request?: HomerApi.FindManyWorkflowRunsRequest, requestOptions?: WorkflowRuns.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyWorkflowRunsResponse>;
    private __findManyWorkflowRuns;
    /**
     * @param {HomerApi.CreateOneWorkflowRunRequest} request
     * @param {WorkflowRuns.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowRuns.createOneWorkflowRun({
     *         body: {
     *             workflowVersionId: "workflowVersionId",
     *             workflowId: "workflowId"
     *         }
     *     })
     */
    createOneWorkflowRun(request: HomerApi.CreateOneWorkflowRunRequest, requestOptions?: WorkflowRuns.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneWorkflowRunResponse>;
    private __createOneWorkflowRun;
    /**
     * @param {HomerApi.CreateManyWorkflowRunsRequest} request
     * @param {WorkflowRuns.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowRuns.createManyWorkflowRuns({
     *         body: [{
     *                 workflowVersionId: "workflowVersionId",
     *                 workflowId: "workflowId"
     *             }]
     *     })
     */
    createManyWorkflowRuns(request: HomerApi.CreateManyWorkflowRunsRequest, requestOptions?: WorkflowRuns.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyWorkflowRunsResponse>;
    private __createManyWorkflowRuns;
    /**
     * **depth** can be provided to request your **workflowRun**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneWorkflowRunRequest} request
     * @param {WorkflowRuns.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowRuns.findOneWorkflowRun("id")
     */
    findOneWorkflowRun(id: string, request?: HomerApi.FindOneWorkflowRunRequest, requestOptions?: WorkflowRuns.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneWorkflowRunResponse>;
    private __findOneWorkflowRun;
    /**
     * @param {string} id - Object id.
     * @param {WorkflowRuns.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowRuns.deleteOneWorkflowRun("id")
     */
    deleteOneWorkflowRun(id: string, requestOptions?: WorkflowRuns.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneWorkflowRunResponse>;
    private __deleteOneWorkflowRun;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.WorkflowRunForUpdate} request
     * @param {WorkflowRuns.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowRuns.updateOneWorkflowRun("id")
     */
    updateOneWorkflowRun(id: string, request?: HomerApi.WorkflowRunForUpdate, requestOptions?: WorkflowRuns.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneWorkflowRunResponse>;
    private __updateOneWorkflowRun;
    /**
     * **depth** can be provided to request your **workflowRun**
     *
     * @param {HomerApi.FindWorkflowRunDuplicatesRequest} request
     * @param {WorkflowRuns.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowRuns.findWorkflowRunDuplicates()
     */
    findWorkflowRunDuplicates(request?: HomerApi.FindWorkflowRunDuplicatesRequest, requestOptions?: WorkflowRuns.RequestOptions): core.HttpResponsePromise<HomerApi.FindWorkflowRunDuplicatesResponse>;
    private __findWorkflowRunDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
