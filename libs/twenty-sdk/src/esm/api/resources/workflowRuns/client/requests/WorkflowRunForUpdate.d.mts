/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface WorkflowRunForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Name of the workflow run */
    name?: string;
    /** Workflow run started at */
    startedAt?: string;
    /** Workflow run ended at */
    endedAt?: string;
    /** Workflow run status */
    status?: WorkflowRunForUpdate.Status;
    /** The executor of the workflow */
    createdBy?: WorkflowRunForUpdate.CreatedBy;
    /** Json object to provide output of the workflow run */
    output?: Record<string, unknown>;
    /** Context */
    context?: Record<string, unknown>;
    /** Workflow run position */
    position?: number;
    /** Workflow version linked to the run. id foreign key */
    workflowVersionId?: string;
    /** Workflow linked to the run. id foreign key */
    workflowId?: string;
}
export declare namespace WorkflowRunForUpdate {
    /**
     * Workflow run status
     */
    type Status = "NOT_STARTED" | "RUNNING" | "COMPLETED" | "FAILED";
    const Status: {
        readonly NotStarted: "NOT_STARTED";
        readonly Running: "RUNNING";
        readonly Completed: "COMPLETED";
        readonly Failed: "FAILED";
    };
    /**
     * The executor of the workflow
     */
    interface CreatedBy {
        source?: CreatedBy.Source;
    }
    namespace CreatedBy {
        type Source = "EMAIL" | "CALENDAR" | "WORKFLOW" | "API" | "IMPORT" | "MANUAL" | "SYSTEM";
        const Source: {
            readonly Email: "EMAIL";
            readonly Calendar: "CALENDAR";
            readonly Workflow: "WORKFLOW";
            readonly Api: "API";
            readonly Import: "IMPORT";
            readonly Manual: "MANUAL";
            readonly System: "SYSTEM";
        };
    }
}
