/**
 * This file was auto-generated by Fern from our API Definition.
 */
export var WorkflowRunForUpdate;
(function (WorkflowRunForUpdate) {
    WorkflowRunForUpdate.Status = {
        NotStarted: "NOT_STARTED",
        Running: "RUNNING",
        Completed: "COMPLETED",
        Failed: "FAILED",
    };
    let CreatedBy;
    (function (CreatedBy) {
        CreatedBy.Source = {
            Email: "EMAIL",
            Calendar: "CALENDAR",
            Workflow: "WORKFLOW",
            Api: "API",
            Import: "IMPORT",
            Manual: "MANUAL",
            System: "SYSTEM",
        };
    })(CreatedBy = WorkflowRunForUpdate.CreatedBy || (WorkflowRunForUpdate.CreatedBy = {}));
})(WorkflowRunForUpdate || (WorkflowRunForUpdate = {}));
