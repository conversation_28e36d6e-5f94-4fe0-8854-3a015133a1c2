/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace WorkflowVersions {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `WorkflowVersions`
 */
export declare class WorkflowVersions {
    protected readonly _options: WorkflowVersions.Options;
    constructor(_options: WorkflowVersions.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **workflowVersions**
     *
     * @param {HomerApi.FindManyWorkflowVersionsRequest} request
     * @param {WorkflowVersions.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowVersions.findManyWorkflowVersions()
     */
    findManyWorkflowVersions(request?: HomerApi.FindManyWorkflowVersionsRequest, requestOptions?: WorkflowVersions.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyWorkflowVersionsResponse>;
    private __findManyWorkflowVersions;
    /**
     * @param {HomerApi.CreateOneWorkflowVersionRequest} request
     * @param {WorkflowVersions.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowVersions.createOneWorkflowVersion({
     *         body: {}
     *     })
     */
    createOneWorkflowVersion(request: HomerApi.CreateOneWorkflowVersionRequest, requestOptions?: WorkflowVersions.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneWorkflowVersionResponse>;
    private __createOneWorkflowVersion;
    /**
     * @param {HomerApi.CreateManyWorkflowVersionsRequest} request
     * @param {WorkflowVersions.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowVersions.createManyWorkflowVersions({
     *         body: [{}]
     *     })
     */
    createManyWorkflowVersions(request: HomerApi.CreateManyWorkflowVersionsRequest, requestOptions?: WorkflowVersions.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyWorkflowVersionsResponse>;
    private __createManyWorkflowVersions;
    /**
     * **depth** can be provided to request your **workflowVersion**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneWorkflowVersionRequest} request
     * @param {WorkflowVersions.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowVersions.findOneWorkflowVersion("id")
     */
    findOneWorkflowVersion(id: string, request?: HomerApi.FindOneWorkflowVersionRequest, requestOptions?: WorkflowVersions.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneWorkflowVersionResponse>;
    private __findOneWorkflowVersion;
    /**
     * @param {string} id - Object id.
     * @param {WorkflowVersions.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowVersions.deleteOneWorkflowVersion("id")
     */
    deleteOneWorkflowVersion(id: string, requestOptions?: WorkflowVersions.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneWorkflowVersionResponse>;
    private __deleteOneWorkflowVersion;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.WorkflowVersionForUpdate} request
     * @param {WorkflowVersions.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowVersions.updateOneWorkflowVersion("id")
     */
    updateOneWorkflowVersion(id: string, request?: HomerApi.WorkflowVersionForUpdate, requestOptions?: WorkflowVersions.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneWorkflowVersionResponse>;
    private __updateOneWorkflowVersion;
    /**
     * **depth** can be provided to request your **workflowVersion**
     *
     * @param {HomerApi.FindWorkflowVersionDuplicatesRequest} request
     * @param {WorkflowVersions.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflowVersions.findWorkflowVersionDuplicates()
     */
    findWorkflowVersionDuplicates(request?: HomerApi.FindWorkflowVersionDuplicatesRequest, requestOptions?: WorkflowVersions.RequestOptions): core.HttpResponsePromise<HomerApi.FindWorkflowVersionDuplicatesResponse>;
    private __findWorkflowVersionDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
