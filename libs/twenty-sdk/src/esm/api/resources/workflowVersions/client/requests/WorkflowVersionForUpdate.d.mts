/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface WorkflowVersionForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** The workflow version name */
    name?: string;
    /** Json object to provide trigger */
    trigger?: Record<string, unknown>;
    /** Json object to provide steps */
    steps?: Record<string, unknown>;
    /** The workflow version status */
    status?: WorkflowVersionForUpdate.Status;
    /** Workflow version position */
    position?: number;
    /** WorkflowVersion workflow id foreign key */
    workflowId?: string;
}
export declare namespace WorkflowVersionForUpdate {
    /**
     * The workflow version status
     */
    type Status = "DRAFT" | "ACTIVE" | "DEACTIVATED" | "ARCHIVED";
    const Status: {
        readonly Draft: "DRAFT";
        readonly Active: "ACTIVE";
        readonly Deactivated: "DEACTIVATED";
        readonly Archived: "ARCHIVED";
    };
}
