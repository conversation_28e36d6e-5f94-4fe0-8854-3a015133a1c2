/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace Workflows {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `Workflows`
 */
export declare class Workflows {
    protected readonly _options: Workflows.Options;
    constructor(_options: Workflows.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **workflows**
     *
     * @param {HomerApi.FindManyWorkflowsRequest} request
     * @param {Workflows.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflows.findManyWorkflows()
     */
    findManyWorkflows(request?: HomerApi.FindManyWorkflowsRequest, requestOptions?: Workflows.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyWorkflowsResponse>;
    private __findManyWorkflows;
    /**
     * @param {HomerApi.CreateOneWorkflowRequest} request
     * @param {Workflows.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflows.createOneWorkflow({
     *         body: {}
     *     })
     */
    createOneWorkflow(request: HomerApi.CreateOneWorkflowRequest, requestOptions?: Workflows.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneWorkflowResponse>;
    private __createOneWorkflow;
    /**
     * @param {HomerApi.CreateManyWorkflowsRequest} request
     * @param {Workflows.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflows.createManyWorkflows({
     *         body: [{}]
     *     })
     */
    createManyWorkflows(request: HomerApi.CreateManyWorkflowsRequest, requestOptions?: Workflows.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyWorkflowsResponse>;
    private __createManyWorkflows;
    /**
     * **depth** can be provided to request your **workflow**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneWorkflowRequest} request
     * @param {Workflows.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflows.findOneWorkflow("id")
     */
    findOneWorkflow(id: string, request?: HomerApi.FindOneWorkflowRequest, requestOptions?: Workflows.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneWorkflowResponse>;
    private __findOneWorkflow;
    /**
     * @param {string} id - Object id.
     * @param {Workflows.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflows.deleteOneWorkflow("id")
     */
    deleteOneWorkflow(id: string, requestOptions?: Workflows.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneWorkflowResponse>;
    private __deleteOneWorkflow;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.WorkflowForUpdate} request
     * @param {Workflows.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflows.updateOneWorkflow("id")
     */
    updateOneWorkflow(id: string, request?: HomerApi.WorkflowForUpdate, requestOptions?: Workflows.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneWorkflowResponse>;
    private __updateOneWorkflow;
    /**
     * **depth** can be provided to request your **workflow**
     *
     * @param {HomerApi.FindWorkflowDuplicatesRequest} request
     * @param {Workflows.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workflows.findWorkflowDuplicates()
     */
    findWorkflowDuplicates(request?: HomerApi.FindWorkflowDuplicatesRequest, requestOptions?: Workflows.RequestOptions): core.HttpResponsePromise<HomerApi.FindWorkflowDuplicatesResponse>;
    private __findWorkflowDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
