/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface WorkflowForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** The workflow name */
    name?: string;
    /** The workflow last published version id */
    lastPublishedVersionId?: string;
    /** The current statuses of the workflow versions */
    statuses?: WorkflowForUpdate.Statuses.Item[];
    /** Workflow record position */
    position?: number;
    /** The creator of the record */
    createdBy?: WorkflowForUpdate.CreatedBy;
}
export declare namespace WorkflowForUpdate {
    type Statuses = Statuses.Item[];
    namespace Statuses {
        type Item = "DRAFT" | "ACTIVE" | "DEACTIVATED";
        const Item: {
            readonly Draft: "DRAFT";
            readonly Active: "ACTIVE";
            readonly Deactivated: "DEACTIVATED";
        };
    }
    /**
     * The creator of the record
     */
    interface CreatedBy {
        source?: CreatedBy.Source;
    }
    namespace CreatedBy {
        type Source = "EMAIL" | "CALENDAR" | "WORKFLOW" | "API" | "IMPORT" | "MANUAL" | "SYSTEM";
        const Source: {
            readonly Email: "EMAIL";
            readonly Calendar: "CALENDAR";
            readonly Workflow: "WORKFLOW";
            readonly Api: "API";
            readonly Import: "IMPORT";
            readonly Manual: "MANUAL";
            readonly System: "SYSTEM";
        };
    }
}
