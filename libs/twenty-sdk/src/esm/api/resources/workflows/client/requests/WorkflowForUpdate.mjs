/**
 * This file was auto-generated by Fern from our API Definition.
 */
export var WorkflowForUpdate;
(function (WorkflowForUpdate) {
    let Statuses;
    (function (Statuses) {
        Statuses.Item = {
            Draft: "DRAFT",
            Active: "ACTIVE",
            Deactivated: "DEACTIVATED",
        };
    })(Statuses = WorkflowForUpdate.Statuses || (WorkflowForUpdate.Statuses = {}));
    let CreatedBy;
    (function (CreatedBy) {
        CreatedBy.Source = {
            Email: "EMAIL",
            Calendar: "CALENDAR",
            Workflow: "WORKFLOW",
            Api: "API",
            Import: "IMPORT",
            Manual: "MANUAL",
            System: "SYSTEM",
        };
    })(CreatedBy = WorkflowForUpdate.CreatedBy || (WorkflowForUpdate.CreatedBy = {}));
})(WorkflowForUpdate || (WorkflowForUpdate = {}));
