/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments.mjs";
import * as core from "../../../../core/index.mjs";
import * as Homer<PERSON><PERSON> from "../../../index.mjs";
export declare namespace WorkspaceMembers {
    interface Options {
        environment?: core.Supplier<environments.HomerApiEnvironment | string>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        token: core.Supplier<core.BearerToken>;
        /** Additional headers to include in requests. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Additional headers to include in the request. */
        headers?: Record<string, string | core.Supplier<string | undefined> | undefined>;
    }
}
/**
 * Object `WorkspaceMembers`
 */
export declare class WorkspaceMembers {
    protected readonly _options: WorkspaceMembers.Options;
    constructor(_options: WorkspaceMembers.Options);
    /**
     * **order_by**, **filter**, **limit**, **depth**, **starting_after** or **ending_before** can be provided to request your **workspaceMembers**
     *
     * @param {HomerApi.FindManyWorkspaceMembersRequest} request
     * @param {WorkspaceMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workspaceMembers.findManyWorkspaceMembers()
     */
    findManyWorkspaceMembers(request?: HomerApi.FindManyWorkspaceMembersRequest, requestOptions?: WorkspaceMembers.RequestOptions): core.HttpResponsePromise<HomerApi.FindManyWorkspaceMembersResponse>;
    private __findManyWorkspaceMembers;
    /**
     * @param {HomerApi.CreateOneWorkspaceMemberRequest} request
     * @param {WorkspaceMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workspaceMembers.createOneWorkspaceMember({
     *         body: {
     *             userId: "userId"
     *         }
     *     })
     */
    createOneWorkspaceMember(request: HomerApi.CreateOneWorkspaceMemberRequest, requestOptions?: WorkspaceMembers.RequestOptions): core.HttpResponsePromise<HomerApi.CreateOneWorkspaceMemberResponse>;
    private __createOneWorkspaceMember;
    /**
     * @param {HomerApi.CreateManyWorkspaceMembersRequest} request
     * @param {WorkspaceMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workspaceMembers.createManyWorkspaceMembers({
     *         body: [{
     *                 userId: "userId"
     *             }]
     *     })
     */
    createManyWorkspaceMembers(request: HomerApi.CreateManyWorkspaceMembersRequest, requestOptions?: WorkspaceMembers.RequestOptions): core.HttpResponsePromise<HomerApi.CreateManyWorkspaceMembersResponse>;
    private __createManyWorkspaceMembers;
    /**
     * **depth** can be provided to request your **workspaceMember**
     *
     * @param {string} id - Object id.
     * @param {HomerApi.FindOneWorkspaceMemberRequest} request
     * @param {WorkspaceMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workspaceMembers.findOneWorkspaceMember("id")
     */
    findOneWorkspaceMember(id: string, request?: HomerApi.FindOneWorkspaceMemberRequest, requestOptions?: WorkspaceMembers.RequestOptions): core.HttpResponsePromise<HomerApi.FindOneWorkspaceMemberResponse>;
    private __findOneWorkspaceMember;
    /**
     * @param {string} id - Object id.
     * @param {WorkspaceMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workspaceMembers.deleteOneWorkspaceMember("id")
     */
    deleteOneWorkspaceMember(id: string, requestOptions?: WorkspaceMembers.RequestOptions): core.HttpResponsePromise<HomerApi.DeleteOneWorkspaceMemberResponse>;
    private __deleteOneWorkspaceMember;
    /**
     * @param {string} id - Object id.
     * @param {HomerApi.WorkspaceMemberForUpdate} request
     * @param {WorkspaceMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workspaceMembers.updateOneWorkspaceMember("id")
     */
    updateOneWorkspaceMember(id: string, request?: HomerApi.WorkspaceMemberForUpdate, requestOptions?: WorkspaceMembers.RequestOptions): core.HttpResponsePromise<HomerApi.UpdateOneWorkspaceMemberResponse>;
    private __updateOneWorkspaceMember;
    /**
     * **depth** can be provided to request your **workspaceMember**
     *
     * @param {HomerApi.FindWorkspaceMemberDuplicatesRequest} request
     * @param {WorkspaceMembers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link HomerApi.BadRequestError}
     * @throws {@link HomerApi.UnauthorizedError}
     *
     * @example
     *     await client.workspaceMembers.findWorkspaceMemberDuplicates()
     */
    findWorkspaceMemberDuplicates(request?: HomerApi.FindWorkspaceMemberDuplicatesRequest, requestOptions?: WorkspaceMembers.RequestOptions): core.HttpResponsePromise<HomerApi.FindWorkspaceMemberDuplicatesResponse>;
    private __findWorkspaceMemberDuplicates;
    protected _getAuthorizationHeader(): Promise<string>;
}
