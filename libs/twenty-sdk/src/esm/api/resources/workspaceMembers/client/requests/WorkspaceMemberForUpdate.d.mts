/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * @example
 *     {}
 */
export interface WorkspaceMemberForUpdate {
    /**
     * Determines the level of nested related objects to include in the response.
     *     - 0: Returns only the primary object's information.
     *     - 1: Returns the primary object along with its directly related objects (with no additional nesting for related objects).
     *     - 2: Returns the primary object, its directly related objects, and the related objects of those related objects.
     */
    depth?: number;
    /** Workspace member position */
    position?: number;
    /** Workspace member name */
    name?: WorkspaceMemberForUpdate.Name;
    /** Preferred color scheme */
    colorScheme?: string;
    /** Preferred language */
    locale?: string;
    /** Workspace member avatar */
    avatarUrl?: string;
    /** Related user email address */
    userEmail?: string;
    /** Associated User Id */
    userId?: string;
    /** User time zone */
    timeZone?: string;
    /** User's preferred date format */
    dateFormat?: WorkspaceMemberForUpdate.DateFormat;
    /** User's preferred time format */
    timeFormat?: WorkspaceMemberForUpdate.TimeFormat;
}
export declare namespace WorkspaceMemberForUpdate {
    /**
     * Workspace member name
     */
    interface Name {
        firstName?: string;
        lastName?: string;
    }
    /**
     * User's preferred date format
     */
    type DateFormat = "SYSTEM" | "MONTH_FIRST" | "DAY_FIRST" | "YEAR_FIRST";
    const DateFormat: {
        readonly System: "SYSTEM";
        readonly MonthFirst: "MONTH_FIRST";
        readonly DayFirst: "DAY_FIRST";
        readonly YearFirst: "YEAR_FIRST";
    };
    /**
     * User's preferred time format
     */
    type TimeFormat = "SYSTEM" | "HOUR_24" | "HOUR_12";
    const TimeFormat: {
        readonly System: "SYSTEM";
        readonly Hour24: "HOUR_24";
        readonly Hour12: "HOUR_12";
    };
}
