import { MigrationInterface, QueryRunner, Table, TableColumn } from 'typeorm';

export class UpdateCampaignsAndAccounts1749222280568
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add column to 'accounts'
    await queryRunner.addColumn(
      'accounts',
      new TableColumn({
        name: 'limite_disparo',
        type: 'integer',
        isNullable: false,
        default: 500,
      }),
    );

    // Add columns to 'campaigns'
    await queryRunner.addColumns('campaigns', [
      new TableColumn({
        name: 'status_envia',
        type: 'integer',
        isNullable: false,
        default: 0,
      }),
      new TableColumn({
        name: 'enviou',
        type: 'integer',
        isNullable: false,
        default: 0,
      }),
      new TableColumn({
        name: 'falhou',
        type: 'integer',
        isNullable: false,
        default: 0,
      }),
    ]);

    // Create the sequence manually
    await queryRunner.query(`
      CREATE SEQUENCE IF NOT EXISTS campaigns_failled_id_seq;
    `);

    // Create table 'campaigns_failled'
    await queryRunner.createTable(
      new Table({
        name: 'campaigns_failled',
        columns: [
          {
            name: 'id',
            type: 'bigint',
            isPrimary: true,
            isNullable: false,
            default: `nextval('campaigns_failled_id_seq')`,
          },
          {
            name: 'nomecontato',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'telefone',
            type: 'character varying',
            isNullable: false,
          },
          {
            name: 'id_campanha',
            type: 'integer',
            isNullable: false,
          },
        ],
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop 'campaigns_failled' table
    await queryRunner.dropTable('campaigns_failled');

    // Drop sequence
    await queryRunner.query(
      `DROP SEQUENCE IF EXISTS campaigns_failled_id_seq;`,
    );

    // Remove added columns from 'campaigns'
    await queryRunner.dropColumn('campaigns', 'status_envia');
    await queryRunner.dropColumn('campaigns', 'enviou');
    await queryRunner.dropColumn('campaigns', 'falhou');

    // Remove 'limite_disparo' from 'accounts'
    await queryRunner.dropColumn('accounts', 'limite_disparo');
  }
}
