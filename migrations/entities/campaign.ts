import {
  Column,
  CreateDate<PERSON><PERSON>umn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

export interface AudienceItem {
  id: number;
  type: string;
}

@Entity({ name: 'campaigns' })
export class Campaign {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ type: 'integer', name: 'display_id' })
  displayId: number;

  @Column({ type: 'varchar' })
  title: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'text' })
  message: string;

  @Column({ type: 'integer', name: 'sender_id', nullable: true })
  senderId?: number;

  @Column({ type: 'boolean', default: true, nullable: true })
  enabled?: boolean;

  @Column({ type: 'bigint', name: 'account_id' })
  accountId: number;

  @Column({ type: 'bigint', name: 'inbox_id' })
  inboxId: number;

  @Column({
    type: 'jsonb',
    name: 'trigger_rules',
    default: () => `'{}'`,
    nullable: true,
  })
  triggerRules?: object;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  updatedAt: Date;

  @Column({ type: 'integer', name: 'campaign_type', default: 0 })
  campaignType: number;

  @Column({ type: 'integer', name: 'campaign_status', default: 0 })
  campaignStatus: number;

  @Column({ type: 'jsonb', default: () => `'[]'`, nullable: true })
  audience?: AudienceItem[];

  @Column({ type: 'timestamp', name: 'scheduled_at', nullable: true })
  scheduledAt?: Date;

  @Column({
    type: 'boolean',
    name: 'trigger_only_during_business_hours',
    default: false,
    nullable: true,
  })
  triggerOnlyDuringBusinessHours?: boolean;

  @Column({ type: 'integer', name: 'status_envia', default: 0 })
  statusEnvia: number;

  @Column({ type: 'integer', default: 0 })
  enviou: number;

  @Column({ type: 'integer', default: 0 })
  falhou: number;
}
