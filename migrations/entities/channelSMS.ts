import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'channel_sms' })
export class ChannelSMS {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'integer', name: 'account_id' })
  accountId: number;

  @Column({ type: 'varchar', name: 'phone_number' })
  phoneNumber: string;

  @Column({ type: 'varchar' })
  provider: string;

  @Column({ type: 'jsonb', name: 'provider_config' })
  providerConfig: Record<string, unknown>;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  updatedAt: Date;
}
