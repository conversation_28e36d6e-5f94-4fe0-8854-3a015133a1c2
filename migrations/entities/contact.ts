import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'contacts' })
export class Contact {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar', nullable: true })
  email?: string;

  @Column({ type: 'varchar', name: 'phone_number', nullable: true })
  phoneNumber?: string;

  @Column({ type: 'integer', name: 'account_id' })
  accountId: number;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  updatedAt: Date;

  @Column({
    type: 'jsonb',
    name: 'additional_attributes',
    default: () => `'{}'`,
  })
  additionalAttributes: Record<string, unknown>;

  @Column({ type: 'varchar' })
  identifier: string;

  @Column({ type: 'jsonb', name: 'custom_attributes', default: () => `'{}'` })
  customAttributes: Record<string, unknown>;

  @Column({ type: 'timestamp', name: 'last_activity_at', nullable: true })
  lastActivityAt?: Date;

  @Column({ type: 'integer', name: 'contact_type', default: 0 })
  contactType: number;

  @Column({ type: 'varchar', name: 'middle_name', nullable: true })
  middleName?: string;

  @Column({ type: 'varchar', name: 'last_name', nullable: true })
  lastName?: string;

  @Column({ type: 'varchar', nullable: true })
  location?: string;

  @Column({ type: 'varchar', name: 'country_code', nullable: true })
  countryCode?: string;

  @Column({ type: 'boolean', default: false })
  blocked: boolean;
}
