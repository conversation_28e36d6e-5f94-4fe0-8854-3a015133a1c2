import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity({ name: 'inboxes' })
export class Inbox {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'integer', name: 'channel_id' })
  channelId: number;

  @Column({ type: 'integer', name: 'account_id' })
  accountId: number;

  @Column({ type: 'varchar' })
  name: string;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp', name: 'updated_at' })
  updatedAt: Date;

  @Column({ type: 'varchar', name: 'channel_type', nullable: true })
  channelType?: string;

  @Column({
    type: 'boolean',
    name: 'enable_auto_assignment',
    default: true,
    nullable: true,
  })
  enableAutoAssignment?: boolean;

  @Column({
    type: 'boolean',
    name: 'greeting_enabled',
    default: false,
    nullable: true,
  })
  greetingEnabled?: boolean;

  @Column({ type: 'varchar', name: 'greeting_message', nullable: true })
  greetingMessage?: string;

  @Column({ type: 'varchar', name: 'email_address', nullable: true })
  emailAddress?: string;

  @Column({
    type: 'boolean',
    name: 'working_hours_enabled',
    default: false,
    nullable: true,
  })
  workingHoursEnabled?: boolean;

  @Column({ type: 'varchar', name: 'out_of_office_message', nullable: true })
  outOfOfficeMessage?: string;

  @Column({ type: 'varchar', nullable: true, default: () => `'UTC'` })
  timezone?: string;

  @Column({
    type: 'boolean',
    name: 'enable_email_collect',
    default: true,
    nullable: true,
  })
  enableEmailCollect?: boolean;

  @Column({
    type: 'boolean',
    name: 'csat_survey_enabled',
    default: false,
    nullable: true,
  })
  csatSurveyEnabled?: boolean;

  @Column({
    type: 'boolean',
    name: 'allow_messages_after_resolved',
    default: true,
    nullable: true,
  })
  allowMessagesAfterResolved?: boolean;

  @Column({
    type: 'jsonb',
    name: 'auto_assignment_config',
    default: () => `'{}'`,
    nullable: true,
  })
  autoAssignmentConfig?: object;

  @Column({
    type: 'boolean',
    name: 'lock_to_single_conversation',
    default: false,
  })
  lockToSingleConversation: boolean;

  @Column({ type: 'bigint', name: 'portal_id', nullable: true })
  portalId?: number;

  @Column({ type: 'integer', name: 'sender_name_type', default: 0 })
  senderNameType: number;

  @Column({ type: 'varchar', name: 'business_name', nullable: true })
  businessName?: string;

  @Column({ type: 'jsonb', name: 'csat_config', default: () => `'{}'` })
  csatConfig: object;
}
