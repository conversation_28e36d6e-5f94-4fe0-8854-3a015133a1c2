import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity({ name: 'taggings' })
export class Tagging {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'integer', name: 'tag_id' })
  tagId: number;

  @Column({ type: 'varchar', name: 'taggable_type' })
  taggableType: string;

  @Column({ type: 'integer', name: 'taggable_id' })
  taggableId: number;

  @Column({ type: 'varchar', name: 'tagger_type', nullable: true })
  taggerType?: string;

  @Column({ type: 'integer', name: 'tagger_id', nullable: true })
  taggerId?: number;

  @Column({ type: 'varchar' })
  context: string;

  @CreateDateColumn({ type: 'timestamp', name: 'created_at' })
  createdAt: Date;
}
