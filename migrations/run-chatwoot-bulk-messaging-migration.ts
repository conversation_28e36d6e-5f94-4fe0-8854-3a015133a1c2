import { ChatwootDataSource } from './chatwoot-datasource';

async function runMigration(): Promise<void> {
  try {
    // eslint-disable-next-line no-console
    console.log('Running migration on custom database...');

    await ChatwootDataSource.initialize();
    await ChatwootDataSource.runMigrations();

    // eslint-disable-next-line no-console
    console.log('✅ Migration completed!');
    process.exit(0);
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error('❌ Error running migration:', err);
    process.exit(1);
  }
}

void runMigration();
