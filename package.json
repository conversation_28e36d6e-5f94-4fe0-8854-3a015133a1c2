{"name": "lisa.workspace", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "packageManager": "pnpm@10.12.1", "scripts": {"build": "turbo build", "build:ai.api": "pnpm --filter @lisa/ai.api build", "build:twilio.api": "pnpm --filter @lisa/twilio.api build", "build:smarters.api": "pnpm --filter @lisa/smarters.api build", "generate:sdk": "pnpm --filter @lisa/twenty-sdk generate", "docker:build:ai.api": "./scripts/docker-build.sh @lisa/ai.api@$(node -p \"require('./apps/ai.api/package.json').version\") apps/ai.api", "docker:build:twilio.api": "./scripts/docker-build.sh @lisa/twilio.api@$(node -p \"require('./apps/twilio.api/package.json').version\") apps/twilio.api", "docker:build:smarters.api": "./scripts/docker-build.sh @lisa/smarters.api@$(node -p \"require('./apps/smarters.api/package.json').version\") apps/smarters.api", "dev": "turbo dev", "start": "turbo start", "test": "turbo test", "lint": "turbo lint", "lint:fix": "turbo lint -- --fix", "format": "turbo format", "format:check": "prettier --check .", "format:write": "prettier --write .", "type-check": "turbo type-check", "pre-dev": "pnpm format:check && pnpm lint && pnpm type-check", "clean": "turbo clean && rm -rf node_modules", "ai": "pnpm --filter @lisa/ai.test dev", "dev:ai.api": "pnpm --filter @lisa/ai.api dev", "dev:ai.test-server": "pnpm --filter @lisa/ai.test-server dev", "dev:webchat": "bash scripts/dev-webchat.sh", "dev:copilotkit.test-client": "pnpm --filter @lisa/copilotkit.test-client dev", "dev:webchat.api": "pnpm --filter @lisa/webchat.api dev", "dev:smarters.api": "pnpm --filter @lisa/smarters.api dev", "dev:copilotkit.api": "pnpm --filter @lisa/copilotkit.api dev", "dev:chatwoot.worker": "pnpm --filter @lisa/chatwoot.worker dev", "dev:agui.test-server": "pnpm --filter @lisa/agui.test-server dev", "dev:webchat.test-client": "pnpm --filter @lisa/webchat.test-client dev", "dev:twilio.api": "pnpm --filter @lisa/twilio.api dev", "dev:aocubo.worker": "pnpm --filter @lisa/aocubo.worker dev", "run-chatwoot-migration": "env-cmd -f migrations/.env ts-node migrations/run-chatwoot-bulk-messaging-migration.ts", "kafka:start": "docker-compose -f docker-compose.dev.yml up -d", "kafka:stop": "docker-compose -f docker-compose.dev.yml down", "kafka:setup": "bash scripts/setup-kafka.sh", "test:tag-validation": "bash scripts/test-tag-validation.sh", "test:tag-validation:error": "bash scripts/test-tag-validation.sh error", "test:tag-validation:ai.api": "bash scripts/test-tag-validation.sh \"\" ai.api", "test:tag-validation:twilio.api": "bash scripts/test-tag-validation.sh \"\" twilio.api", "test:tag-validation:smarters.api": "bash scripts/test-tag-validation.sh \"\" smarters.api", "test:fallback-validation": "bash scripts/test-fallback-validation.sh", "create-tag": "bash scripts/create-tag.sh", "create-tag:ai.api": "bash scripts/create-tag.sh ai.api", "create-tag:twilio.api": "bash scripts/create-tag.sh twilio.api", "create-tag:smarters.api": "bash scripts/create-tag.sh smarters.api", "prepare": "husky", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "dependencies": {"dotenv": "^16.5.0", "env-cmd": "10.1.0", "typeorm": "^0.3.25"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@types/node": "^22.10.7", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.4.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "turbo": "^2.5.4", "typescript": "^5.8.3", "typescript-eslint": "^8.20.0"}, "jest": {"projects": ["<rootDir>/apps/*/jest.config.js", "<rootDir>/libs/*/jest.config.js"]}}