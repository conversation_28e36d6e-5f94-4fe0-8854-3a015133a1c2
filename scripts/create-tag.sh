#!/bin/bash

# Script para criar tags com validação automática
#
# Usage: ./scripts/create-tag.sh <projeto> [versão]
# 
# Se versão não for fornecida, usa a versão do package.json
# 
# Examples:
#   ./scripts/create-tag.sh ai.api                    # Usa versão do package.json
#   ./scripts/create-tag.sh ai.api 1.0.0              # Usa versão específica
#   ./scripts/create-tag.sh twilio.api 0.0.2          # Cria @lisa/twilio.api@0.0.2

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "\n${CYAN}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}  $1${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════${NC}\n"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check arguments
if [ $# -lt 1 ]; then
    print_error "Usage: $0 <projeto> [versão]"
    print_info "Projetos disponíveis: ai.api, twilio.api, smarters.api"
    print_info "Examples:"
    print_info "  $0 ai.api                    # Usa versão do package.json"
    print_info "  $0 ai.api 1.0.0              # Usa versão específica"
    exit 1
fi

PROJECT="$1"
CUSTOM_VERSION="$2"

print_header "CRIAÇÃO DE TAG COM VALIDAÇÃO"

# Validate project
PROJECT_DIR="apps/$PROJECT"
PACKAGE_JSON="$PROJECT_DIR/package.json"

print_info "🔍 Validando projeto: $PROJECT"

if [ ! -d "$PROJECT_DIR" ]; then
    print_error "Diretório do projeto não encontrado: $PROJECT_DIR"
    print_info "Projetos disponíveis:"
    ls -1 apps/ | grep -E '\.(api|worker)$' | sed 's/^/  • /'
    exit 1
fi

if [ ! -f "$PACKAGE_JSON" ]; then
    print_error "Package.json não encontrado: $PACKAGE_JSON"
    exit 1
fi

print_success "Projeto encontrado: $PROJECT_DIR"

# Get package info
PACKAGE_NAME=$(node -p "require('./$PACKAGE_JSON').name")
PACKAGE_VERSION=$(node -p "require('./$PACKAGE_JSON').version")

print_info "📦 Nome no package.json: $PACKAGE_NAME"
print_info "📦 Versão no package.json: $PACKAGE_VERSION"

# Determine version to use
if [ -n "$CUSTOM_VERSION" ]; then
    USE_VERSION="$CUSTOM_VERSION"
    print_warning "🔢 Usando versão customizada: $USE_VERSION"
    
    if [ "$USE_VERSION" != "$PACKAGE_VERSION" ]; then
        print_warning "⚠️  Versão customizada ($USE_VERSION) difere do package.json ($PACKAGE_VERSION)"
        print_info "Deseja continuar? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            print_error "Operação cancelada pelo usuário!"
            exit 1
        fi
    fi
else
    USE_VERSION="$PACKAGE_VERSION"
    print_info "🔢 Usando versão do package.json: $USE_VERSION"
fi

# Create tag name
TAG_NAME="$PACKAGE_NAME@$USE_VERSION"

print_info "🏷️  Tag a ser criada: $TAG_NAME"

# Check if tag already exists
if git tag -l | grep -q "^$TAG_NAME$"; then
    print_error "Tag já existe: $TAG_NAME"
    print_info "Para remover a tag existente:"
    print_info "  git tag -d $TAG_NAME"
    print_info "  git push origin :refs/tags/$TAG_NAME"
    exit 1
fi

# Validate tag before creating
print_info "🔍 Validando tag antes da criação..."
if ! node scripts/validate-tag.js "$TAG_NAME" "$PROJECT_DIR"; then
    print_error "Validação da tag falhou!"
    exit 1
fi

print_success "Validação passou!"

# Check if project has Dockerfile (supports Docker deployment)
DOCKERFILE="$PROJECT_DIR/Dockerfile"
if [ -f "$DOCKERFILE" ]; then
    print_success "✅ Projeto suporta deploy via Docker"
    SUPPORTS_DOCKER=true
else
    print_warning "⚠️  Projeto não tem Dockerfile - não fará deploy automático"
    SUPPORTS_DOCKER=false
fi

# Show summary
print_header "RESUMO DA OPERAÇÃO"
print_info "📦 Projeto: $PROJECT"
print_info "🏷️  Tag: $TAG_NAME"
print_info "📁 Diretório: $PROJECT_DIR"
print_info "🐳 Deploy Docker: $([ "$SUPPORTS_DOCKER" = true ] && echo "✅ Sim" || echo "❌ Não")"

if [ "$SUPPORTS_DOCKER" = true ]; then
    print_info "🚀 Esta tag disparará pipeline automática no Bitbucket"
else
    print_warning "⚠️  Esta tag NÃO disparará pipeline (sem Dockerfile)"
fi

print_info ""
print_info "Confirmar criação da tag? (y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    print_error "Operação cancelada pelo usuário!"
    exit 1
fi

# Create tag
print_info "🏷️  Criando tag: $TAG_NAME"
git tag "$TAG_NAME"

print_success "Tag criada com sucesso!"

# Ask about pushing
print_info ""
print_info "Deseja fazer push da tag agora? (y/N)"
read -r push_response
if [[ "$push_response" =~ ^[Yy]$ ]]; then
    print_info "📤 Fazendo push da tag..."
    git push origin "$TAG_NAME"
    
    if [ "$SUPPORTS_DOCKER" = true ]; then
        print_success "🚀 Tag enviada! Pipeline será iniciada automaticamente."
        print_info "📊 Monitore o progresso em: Bitbucket → Pipelines"
    else
        print_success "📤 Tag enviada!"
    fi
else
    print_info "💾 Tag criada localmente. Para enviar depois:"
    print_info "  git push origin $TAG_NAME"
fi

print_header "PRÓXIMOS PASSOS"
if [ "$SUPPORTS_DOCKER" = true ]; then
    print_info "🔍 Monitorar pipeline no Bitbucket"
    print_info "🧪 Testar localmente: pnpm test:tag-validation:$PROJECT"
else
    print_info "🧪 Testar validação: pnpm test:tag-validation:$PROJECT"
fi

print_success "🎉 Operação concluída!"
