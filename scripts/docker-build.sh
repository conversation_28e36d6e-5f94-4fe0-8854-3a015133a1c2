#!/bin/bash

# Script para build de imagens Docker com validação
#
# Usage: ./scripts/docker-build.sh <tag> <project-path> [push]
# Example: ./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api
# Example: ./scripts/docker-build.sh @lisa/ai.api@0.0.1 apps/ai.api push

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check arguments
if [ $# -lt 2 ]; then
    print_error "Usage: $0 <tag> <project-path> [push]"
    print_info "Example: $0 @lisa/ai.api@0.0.1 apps/ai.api"
    print_info "Example: $0 @lisa/ai.api@0.0.1 apps/ai.api push"
    exit 1
fi

TAG="$1"
PROJECT_PATH="$2"
SHOULD_PUSH="$3"

print_info "🚀 Starting Docker build process"
print_info "Tag: $TAG"
print_info "Project: $PROJECT_PATH"

# Step 1: Validate tag against package.json
print_info "🔍 Validating tag against package.json..."
if ! node scripts/validate-tag.js "$TAG" "$PROJECT_PATH"; then
    print_error "Tag validation failed!"
    exit 1
fi
print_success "Tag validation passed!"

# Step 2: Extract project name and version from tag
PROJECT_NAME=$(echo "$TAG" | sed 's/@lisa\///' | sed 's/@.*//')
TAG_VERSION=$(echo "$TAG" | sed 's/.*@//')

print_info "📦 Project name: $PROJECT_NAME"
print_info "🏷️  Version: $TAG_VERSION"

# Step 3: Check if Dockerfile exists
DOCKERFILE_PATH="$PROJECT_PATH/Dockerfile"
if [ ! -f "$DOCKERFILE_PATH" ]; then
    print_error "Dockerfile not found at: $DOCKERFILE_PATH"
    exit 1
fi
print_success "Dockerfile found: $DOCKERFILE_PATH"

# Step 4: Build Docker image
IMAGE_NAME="lisa-$PROJECT_NAME"
IMAGE_TAG="$IMAGE_NAME:$TAG_VERSION"

print_info "🐳 Building Docker image: $IMAGE_TAG"

# Build with build args for project context
docker build \
    --build-arg PROJECT_NAME="$PROJECT_NAME" \
    --build-arg PROJECT_PATH="$PROJECT_PATH" \
    --build-arg TAG_VERSION="$TAG_VERSION" \
    --tag "$IMAGE_TAG" \
    --file "$DOCKERFILE_PATH" \
    .

if [ $? -eq 0 ]; then
    print_success "Docker image built successfully: $IMAGE_TAG"
else
    print_error "Docker build failed!"
    exit 1
fi

# Step 5: Tag with latest if this is the main version
print_info "🏷️  Tagging as latest..."
docker tag "$IMAGE_TAG" "$IMAGE_NAME:latest"
print_success "Tagged as: $IMAGE_NAME:latest"

# Step 6: Push if requested
if [ "$SHOULD_PUSH" = "push" ]; then
    print_info "📤 Pushing image to registry..."

    # Check if registry variables are set
    if [ -z "$CR_HOSTNAME" ]; then
        print_warning "CR_HOSTNAME not set, skipping registry push"
        print_info "To push to registry, set: CR_HOSTNAME, CR_USERNAME, CR_PASSWORD"
    else
        # Login to registry
        print_info "🔐 Logging into registry: $CR_HOSTNAME"
        echo "$CR_PASSWORD" | docker login "$CR_HOSTNAME" --username "$CR_USERNAME" --password-stdin

        # Tag for registry
        REGISTRY_IMAGE="$CR_HOSTNAME/$IMAGE_TAG"
        REGISTRY_LATEST="$CR_HOSTNAME/$IMAGE_NAME:latest"

        docker tag "$IMAGE_TAG" "$REGISTRY_IMAGE"
        docker tag "$IMAGE_NAME:latest" "$REGISTRY_LATEST"

        # Push both tags
        print_info "📤 Pushing: $REGISTRY_IMAGE"
        docker push "$REGISTRY_IMAGE"

        print_info "📤 Pushing: $REGISTRY_LATEST"
        docker push "$REGISTRY_LATEST"

        print_success "Images pushed to registry successfully!"
    fi
else
    print_info "💾 Image built locally. To push, add 'push' parameter:"
    print_info "   $0 $TAG $PROJECT_PATH push"
fi

print_success "🎉 Docker build process completed!"
print_info "Local image: $IMAGE_TAG"
if [ -n "$CR_HOSTNAME" ] && [ "$SHOULD_PUSH" = "push" ]; then
    print_info "Registry image: $CR_HOSTNAME/$IMAGE_TAG"
fi
