services:
  evolution_api:
    container_name: evolution_api
    image: atendai/evolution-api:v2.2.3
    restart: always
    depends_on:
      - redis
      - postgres
    ports:
      - 8080:8080
    volumes:
      - evolution_instances:/evolution/instances
    env_file:
      - .env.evolution
    expose:
      - 8080

  chatwoot_base: &chatwoot_base
    image: chatwoot/chatwoot:v4.1.0
    env_file: .env.chatwoot ## Change this file for customized env variables
    volumes:
      - data_chatwoot:/app/storage

  chatwoot_db_init:
    <<: *chatwoot_base
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    environment:
      - NODE_ENV=production
      - RAILS_ENV=production
      - INSTALLATION_ENV=docker
    command: ['bundle', 'exec', 'rails', 'db:chatwoot_prepare']
    restart: 'no'

  chatwoot_rails:
    <<: *chatwoot_base
    depends_on:
      - postgres
      - redis
      - chatwoot_db_init
    ports:
      - 3000:3000
    environment:
      - NODE_ENV=production
      - RAILS_ENV=production
      - INSTALLATION_ENV=docker
    entrypoint: docker/entrypoints/rails.sh
    command: ['bundle', 'exec', 'rails', 's', '-p', '3000', '-b', '0.0.0.0']
    restart: always

  chatwoot_sidekiq:
    <<: *chatwoot_base
    depends_on:
      - postgres
      - redis
      - chatwoot_db_init
    environment:
      - NODE_ENV=production
      - RAILS_ENV=production
      - INSTALLATION_ENV=docker
    command: ['bundle', 'exec', 'sidekiq', '-C', 'config/sidekiq.yml']
    restart: always

  postgres:
    container_name: postgres
    image: pgvector/pgvector:pg16
    restart: always
    # command: ["postgres", "-c", "max_connections=1000", "-c", "listen_addresses=*"]
    ports:
      - 5432:5432
    volumes:
      - data_postgres:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=chatwoot
      - POSTGRES_USER=postgres
      # Please provide your own password.
      - POSTGRES_PASSWORD=password
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres -d chatwoot']
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    container_name: redis
    image: redis:7-alpine
    restart: always
    # command: ["sh", "-c", "redis-server --requirepass \"$REDIS_PASSWORD\""]
    # command: >
    #   redis-server --port 6379 --appendonly yes
    # env_file: .env
    volumes:
      - data_redis:/data
    ports:
      - 6379:6379

volumes:
  evolution_instances:
  data_chatwoot:
  data_postgres:
  data_redis:
