#!/bin/bash

# Script para testar o sistema de fallback de validação de tags
#
# Usage: ./scripts/test-fallback-validation.sh
# 
# Testa diferentes tipos de tags que não seguem o padrão Lisa

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "\n${CYAN}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}  $1${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════${NC}\n"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "\n${YELLOW}🔸 $1${NC}"
}

print_header "TESTE DO SISTEMA DE FALLBACK DE TAGS"

print_info "Este script demonstra como o sistema de fallback funciona para diferentes tipos de tags."
print_info "Todos estes cenários seriam capturados pela pipeline de fallback no Bitbucket."
echo

# Test cases for different tag formats
TEST_CASES=(
    "v1.0.0:Semantic Versioning"
    "1.0.0:Versão simples"
    "release-1.0.0:Release tag"
    "ai.api-1.0.0:Projeto com formato incorreto"
    "@other/project@1.0.0:Escopo diferente"
    "random-tag:Tag aleatória"
    "@lisa/projeto-inexistente@1.0.0:Projeto Lisa inexistente"
    "twilio.api-v1.0.0:Projeto Twilio formato incorreto"
    "smarters-api-release:Projeto Smarters formato incorreto"
)

# Test each case
for test_case in "${TEST_CASES[@]}"; do
    IFS=':' read -r tag_name description <<< "$test_case"
    
    print_step "TESTE: $description"
    print_info "Tag: $tag_name"
    
    echo -e "${YELLOW}Executando validação de fallback...${NC}"
    
    # Test the fallback validation
    if [[ "$tag_name" =~ ^@lisa/ ]]; then
        # This would be caught by the @lisa/* fallback
        print_info "📋 Seria capturado por: Pipeline '@lisa/*'"
        if ./scripts/validate-unknown-tag.sh "$tag_name" 2>/dev/null; then
            print_success "✅ Validação de fallback Lisa executada"
        else
            print_warning "⚠️  Validação de fallback Lisa detectou problema"
        fi
    else
        # This would be caught by the default fallback
        print_info "📋 Seria capturado por: Pipeline 'default'"
        if ./scripts/validate-any-tag.sh "$tag_name" 2>/dev/null; then
            print_success "✅ Validação de fallback geral executada"
        else
            print_warning "⚠️  Validação de fallback geral detectou possível erro"
        fi
    fi
    
    echo
done

print_header "RESUMO DO SISTEMA DE FALLBACK"

print_info "🎯 Objetivo: Capturar e orientar sobre qualquer tag criada"
print_info ""
print_info "📋 Pipelines configuradas:"
print_info "   1. '@lisa/ai.api@*' - Deploy automático do AI API"
print_info "   2. '@lisa/twilio.api@*' - Deploy automático do Twilio API"
print_info "   3. '@lisa/smarters.api@*' - Deploy automático do Smarters API"
print_info "   4. '@lisa/*' - Fallback para tags Lisa não reconhecidas"
print_info "   5. 'default' - Fallback universal para qualquer outra tag"
print_info ""
print_info "🔄 Fluxo de captura:"
print_info "   • Tag específica → Deploy automático"
print_info "   • Tag @lisa/* não reconhecida → Orientação específica Lisa"
print_info "   • Qualquer outra tag → Orientação geral + detecção de erros"

print_header "BENEFÍCIOS DO SISTEMA ABRANGENTE"

print_info "✅ Vantagens:"
print_info "   • Nenhuma tag fica sem feedback"
print_info "   • Detecção automática de possíveis erros"
print_info "   • Orientação específica por tipo de tag"
print_info "   • Prevenção de confusão sobre tags não funcionais"
print_info "   • Log completo de todas as tentativas de tag"

print_header "EXEMPLOS DE ORIENTAÇÕES FORNECIDAS"

print_info "🔧 Para 'v1.0.0':"
print_info "   → Sugere usar @lisa/{projeto}@1.0.0 para deploy específico"
print_info ""
print_info "🔧 Para 'ai.api-1.0.0':"
print_info "   → Detecta nome de projeto e sugere @lisa/ai.api@1.0.0"
print_info ""
print_info "🔧 Para '@lisa/projeto-inexistente@1.0.0':"
print_info "   → Lista projetos disponíveis e como adicionar novos"
print_info ""
print_info "🔧 Para 'random-tag':"
print_info "   → Explica sistema de tags Lisa e fornece documentação"

print_success "🎉 Teste do sistema de fallback concluído!"
print_info ""
print_info "💡 Para testar localmente:"
print_info "   ./scripts/validate-unknown-tag.sh @lisa/projeto-inexistente@1.0.0"
print_info "   ./scripts/validate-any-tag.sh v1.0.0"
print_info "   ./scripts/validate-any-tag.sh ai.api-1.0.0"
