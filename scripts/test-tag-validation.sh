#!/bin/bash

# Script para testar a validação de tags do <PERSON><PERSON> build
#
# Usage: ./scripts/test-tag-validation.sh [error] [projeto]
#
# Parâmetros:
#   error: força apenas cenários de erro para demonstração
#   projeto: testa apenas um projeto específico (ai.api, twilio.api, smarters.api)
#
# Exemplos:
#   ./scripts/test-tag-validation.sh                    # Testa todos os projetos (sucesso + erro)
#   ./scripts/test-tag-validation.sh error              # Testa todos os projetos (apenas erros)
#   ./scripts/test-tag-validation.sh error ai.api       # Testa apenas ai.api (apenas erros)
#   ./scripts/test-tag-validation.sh "" twilio.api      # Testa apenas twilio.api (sucesso + erro)

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "\n${CYAN}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}  $1${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════${NC}\n"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_step() {
    echo -e "\n${YELLOW}🔸 $1${NC}"
}

# Check if we should only test error scenarios
FORCE_ERROR="$1"
PROJECT_FILTER="$2"

print_header "TESTE DE VALIDAÇÃO DE TAGS - DOCKER BUILD"

print_info "Este script demonstra como funciona a validação de tags no processo de build do Docker."
print_info "A validação garante que a tag Git corresponda ao nome e versão do package.json."
echo

# Projects with Docker support
PROJECTS=("ai.api" "twilio.api" "smarters.api")

# Filter projects if specified
if [ -n "$PROJECT_FILTER" ]; then
    if [[ " ${PROJECTS[@]} " =~ " ${PROJECT_FILTER} " ]]; then
        PROJECTS=("$PROJECT_FILTER")
        print_info "🎯 Testando apenas projeto: $PROJECT_FILTER"
    else
        print_error "Projeto não encontrado: $PROJECT_FILTER"
        print_info "Projetos disponíveis: ${PROJECTS[*]}"
        exit 1
    fi
else
    print_info "🎯 Testando todos os projetos com Docker: ${PROJECTS[*]}"
fi
echo

# Function to test a single project
test_project() {
    local project="$1"
    local package_json="apps/$project/package.json"

    print_header "TESTANDO PROJETO: $project"

    if [ ! -f "$package_json" ]; then
        print_error "Package.json não encontrado: $package_json"
        return 1
    fi

    local current_name=$(node -p "require('./$package_json').name")
    local current_version=$(node -p "require('./$package_json').version")
    local expected_tag="$current_name@$current_version"

    print_info "📦 Projeto: apps/$project"
    print_info "📦 Nome atual: $current_name"
    print_info "📦 Versão atual: $current_version"
    print_info "🏷️  Tag esperada: $expected_tag"

    if [ "$FORCE_ERROR" != "error" ]; then
        # Test 1: Valid tag (should succeed)
        print_step "TESTE 1: Tag válida (deve passar na validação)"
        print_info "Testando com tag: $expected_tag"

        if node scripts/validate-tag.js "$expected_tag" "apps/$project"; then
            print_success "✅ Validação passou como esperado!"
        else
            print_error "❌ Validação falhou inesperadamente!"
        fi
    fi

    # Test 2: Invalid tag - wrong version (should fail)
    print_step "TESTE 2: Tag com versão incorreta (deve falhar na validação)"
    local wrong_version_tag="$current_name@9.9.9"
    print_info "Testando com tag: $wrong_version_tag"

    echo -e "${YELLOW}Executando validação...${NC}"
    if node scripts/validate-tag.js "$wrong_version_tag" "apps/$project"; then
        print_error "❌ Validação passou quando deveria falhar!"
    else
        print_success "✅ Validação falhou como esperado!"
    fi

    # Test 3: Invalid tag - wrong name (should fail)
    print_step "TESTE 3: Tag com nome incorreto (deve falhar na validação)"
    local wrong_name_tag="@lisa/wrong.$project@$current_version"
    print_info "Testando com tag: $wrong_name_tag"

    echo -e "${YELLOW}Executando validação...${NC}"
    if node scripts/validate-tag.js "$wrong_name_tag" "apps/$project"; then
        print_error "❌ Validação passou quando deveria falhar!"
    else
        print_success "✅ Validação falhou como esperado!"
    fi

    # Test 4: Invalid tag format (should fail)
    print_step "TESTE 4: Tag com formato incorreto (deve falhar na validação)"
    local wrong_format_tag="invalid-tag-format"
    print_info "Testando com tag: $wrong_format_tag"

    echo -e "${YELLOW}Executando validação...${NC}"
    if node scripts/validate-tag.js "$wrong_format_tag" "apps/$project"; then
        print_error "❌ Validação passou quando deveria falhar!"
    else
        print_success "✅ Validação falhou como esperado!"
    fi

    # Test 5: Docker build with invalid tag (should fail)
    print_step "TESTE 5: Docker build com tag inválida (demonstração completa)"
    print_info "Testando build completo com tag: $wrong_version_tag"
    print_warning "Este teste vai falhar propositalmente para demonstrar a validação"
    print_info "Executando: ./scripts/docker-build.sh $wrong_version_tag apps/$project"

    echo -e "${YELLOW}Executando docker build...${NC}"
    if ./scripts/docker-build.sh "$wrong_version_tag" "apps/$project" 2>/dev/null; then
        print_error "❌ Docker build passou quando deveria falhar!"
    else
        print_success "✅ Docker build falhou como esperado devido à validação de tag!"
        print_info "💡 O build foi interrompido na etapa de validação, antes mesmo de tentar construir a imagem"
    fi

    echo
}

# Run tests for all projects
for project in "${PROJECTS[@]}"; do
    test_project "$project"
done

print_header "RESUMO DOS TESTES"

print_info "🎯 Objetivo: Demonstrar como a validação de tags funciona"
print_info "📋 Projetos testados: ${PROJECTS[*]}"
print_info "📋 Testes executados por projeto:"
if [ "$FORCE_ERROR" != "error" ]; then
    print_info "   1. ✅ Tag válida - passou na validação"
fi
print_info "   2. ❌ Tag com versão incorreta - falhou na validação"
print_info "   3. ❌ Tag com nome incorreto - falhou na validação"
print_info "   4. ❌ Tag com formato incorreto - falhou na validação"
print_info "   5. ❌ Docker build com tag inválida - falhou no build"

print_header "COMO CORRIGIR ERROS DE VALIDAÇÃO"

print_info "💡 Para corrigir erros de validação de tag:"
print_info "   1. Verifique o nome e versão no package.json do projeto"
print_info "   2. A tag deve seguir o formato: {nome-do-package}@{versão}"
print_info "   3. Exemplos corretos:"
print_info "      • @lisa/ai.api@0.0.1"
print_info "      • @lisa/twilio.api@0.0.1"
print_info "      • @lisa/smarters.api@0.0.1"
print_info ""
print_info "🔧 Comandos para criar tag correta:"
print_info "   git tag @lisa/{projeto}@{versão}"
print_info "   git push origin @lisa/{projeto}@{versão}"
print_info ""
print_info "📝 Ou atualize a versão no package.json e crie nova tag:"
print_info "   1. Edite apps/{projeto}/package.json"
print_info "   2. Atualize o campo 'version'"
print_info "   3. git tag {nome}@{nova-versão}"
print_info "   4. git push origin {nome}@{nova-versão}"

print_success "🎉 Teste de validação de tags concluído!"
