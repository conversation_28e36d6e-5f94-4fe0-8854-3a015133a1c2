#!/bin/bash

# Script para validar qualquer tag que não seja do padrão Lisa
#
# Usage: ./scripts/validate-any-tag.sh <tag>
# Example: ./scripts/validate-any-tag.sh v1.0.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "\n${CYAN}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}  $1${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════${NC}\n"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check arguments
if [ $# -lt 1 ]; then
    print_error "Usage: $0 <tag>"
    print_info "Example: $0 v1.0.0"
    exit 1
fi

TAG="$1"

print_header "VALIDAÇÃO DE TAG NÃO-LISA"

print_info "Tag detectada: $TAG"
print_info "Esta tag não segue o padrão do projeto Lisa."

# Analyze tag format
print_info ""
print_info "🔍 Análise da tag:"

# Check common patterns
if [[ "$TAG" =~ ^v[0-9]+\.[0-9]+\.[0-9]+.*$ ]]; then
    print_info "   • Formato: Semantic Versioning (v1.0.0)"
    TAG_TYPE="semver"
elif [[ "$TAG" =~ ^[0-9]+\.[0-9]+\.[0-9]+.*$ ]]; then
    print_info "   • Formato: Versão simples (1.0.0)"
    TAG_TYPE="version"
elif [[ "$TAG" =~ ^release-.*$ ]]; then
    print_info "   • Formato: Release tag (release-*)"
    TAG_TYPE="release"
elif [[ "$TAG" =~ ^@[^/]+/.*$ ]]; then
    print_info "   • Formato: Scoped package (@org/package)"
    TAG_TYPE="scoped"
else
    print_info "   • Formato: Tag personalizada"
    TAG_TYPE="custom"
fi

print_info "   • Tipo identificado: $TAG_TYPE"

# Provide guidance based on tag type
print_header "ORIENTAÇÕES"

case "$TAG_TYPE" in
    "semver"|"version")
        print_warning "Esta tag parece ser uma versão geral do projeto."
        print_info "💡 Se for para deploy de uma aplicação específica:"
        print_info "   • Use o padrão Lisa: @lisa/{projeto}@{versão}"
        print_info "   • Exemplo: @lisa/ai.api@1.0.0"
        ;;
    "release")
        print_warning "Esta tag parece ser um release geral."
        print_info "💡 Para deploy automático de aplicações:"
        print_info "   • Use tags específicas por projeto"
        print_info "   • Exemplo: @lisa/ai.api@1.0.0"
        ;;
    "scoped")
        if [[ "$TAG" =~ ^@lisa/ ]]; then
            print_error "Esta tag usa o escopo @lisa mas não está configurada!"
            print_info "💡 Verifique se o nome do projeto está correto:"
            print_info "   • Projetos disponíveis: ai.api, twilio.api, smarters.api"
        else
            print_info "Esta tag usa um escopo diferente de @lisa."
            print_info "💡 Para projetos Lisa, use: @lisa/{projeto}@{versão}"
        fi
        ;;
    "custom")
        print_info "Tag personalizada detectada."
        print_info "💡 Para deploy automático, use o padrão Lisa."
        ;;
esac

# Show Lisa project information
print_header "PROJETOS LISA DISPONÍVEIS"

print_info "📋 Projetos configurados para deploy automático:"
print_info "   • @lisa/ai.api@* - API de IA baseada em Mastra"
print_info "   • @lisa/twilio.api@* - API de comunicação SMS/voz"
print_info "   • @lisa/smarters.api@* - API de integração Chatwoot"

print_info ""
print_info "🎯 Para fazer deploy de uma aplicação específica:"
print_info "   1. Identifique o projeto (ai.api, twilio.api, smarters.api)"
print_info "   2. Atualize a versão no package.json do projeto"
print_info "   3. Crie a tag: git tag @lisa/{projeto}@{versão}"
print_info "   4. Faça push: git push origin @lisa/{projeto}@{versão}"

# Check if this might be a mistake
print_header "VERIFICAÇÃO DE POSSÍVEIS ERROS"

# Check if tag name contains project names
if [[ "$TAG" =~ ai\.api|twilio\.api|smarters\.api ]]; then
    print_warning "🚨 A tag contém nome de projeto Lisa!"
    print_info "Possível tag incorreta detectada."
    
    if [[ "$TAG" =~ ai\.api ]]; then
        SUGGESTED_PROJECT="ai.api"
    elif [[ "$TAG" =~ twilio\.api ]]; then
        SUGGESTED_PROJECT="twilio.api"
    elif [[ "$TAG" =~ smarters\.api ]]; then
        SUGGESTED_PROJECT="smarters.api"
    fi
    
    if [ -n "$SUGGESTED_PROJECT" ]; then
        # Try to extract version
        if [[ "$TAG" =~ ([0-9]+\.[0-9]+\.[0-9]+) ]]; then
            SUGGESTED_VERSION="${BASH_REMATCH[1]}"
            SUGGESTED_TAG="@lisa/$SUGGESTED_PROJECT@$SUGGESTED_VERSION"
            
            print_info "💡 Tag sugerida: $SUGGESTED_TAG"
            print_info "🔧 Para corrigir:"
            print_info "   git tag -d $TAG"
            print_info "   git push origin :refs/tags/$TAG"
            print_info "   git tag $SUGGESTED_TAG"
            print_info "   git push origin $SUGGESTED_TAG"
        fi
    fi
fi

print_header "FERRAMENTAS DISPONÍVEIS"

print_info "🛠️ Scripts para facilitar o processo:"
print_info "   • pnpm create-tag:ai.api - Criar tag do AI API"
print_info "   • pnpm create-tag:twilio.api - Criar tag do Twilio API"
print_info "   • pnpm create-tag:smarters.api - Criar tag do Smarters API"
print_info "   • pnpm test:tag-validation - Testar validação de tags"

print_info ""
print_info "📚 Documentação:"
print_info "   • docs/TAG_VALIDATION_SYSTEM.md - Sistema completo"
print_info "   • docs/DEPLOY_GUIDE.md - Guia de deploy"
print_info "   • docs/QUICK_DEPLOY_REFERENCE.md - Referência rápida"

# Determine exit code
if [[ "$TAG" =~ ^@lisa/ ]] || [[ "$TAG" =~ ai\.api|twilio\.api|smarters\.api ]]; then
    print_warning "⚠️  Tag possivelmente incorreta - pipeline falhará intencionalmente."
    exit 1
else
    print_success "✅ Tag não-Lisa processada. Nenhuma ação de deploy será executada."
    exit 0
fi
