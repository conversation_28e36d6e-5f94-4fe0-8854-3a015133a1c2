#!/usr/bin/env node

/**
 * Script para validar se a tag Git corresponde ao nome e versão do package.json
 *
 * Padrão esperado da tag: {package-name}@{version} (seguin<PERSON> <PERSON><PERSON>)
 * Exemplo: @lisa/ai.api@0.0.1, @lisa/twilio.api@0.0.1
 *
 * Usage: node scripts/validate-tag.js <tag> <project-path>
 * Example: node scripts/validate-tag.js @lisa/ai.api@0.0.1 apps/ai.api
 */

const fs = require('fs');
const path = require('path');

function main() {
  const [, , tag, projectPath] = process.argv;

  if (!tag || !projectPath) {
    console.error(
      '❌ Usage: node scripts/validate-tag.js <tag> <project-path>',
    );
    console.error(
      '   Example: node scripts/validate-tag.js @lisa/ai.api@0.0.1 apps/ai.api',
    );
    process.exit(1);
  }

  console.log(`🔍 Validating tag: ${tag} for project: ${projectPath}`);

  // Verificar se o diretório do projeto existe
  if (!fs.existsSync(projectPath)) {
    console.error(`❌ Project directory not found: ${projectPath}`);
    process.exit(1);
  }

  // Verificar se o package.json existe
  const packageJsonPath = path.join(projectPath, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    console.error(`❌ package.json not found in: ${projectPath}`);
    process.exit(1);
  }

  // Ler o package.json
  let packageJson;
  try {
    const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8');
    packageJson = JSON.parse(packageJsonContent);
  } catch (error) {
    console.error(`❌ Error reading package.json: ${error.message}`);
    process.exit(1);
  }

  // Extrair nome e versão do package.json
  const { name, version } = packageJson;
  if (!name || !version) {
    console.error('❌ package.json must have both "name" and "version" fields');
    process.exit(1);
  }

  // Construir a tag esperada seguindo padrão Mastra: package-name@version
  const expectedTag = `${name}@${version}`;

  console.log(`📦 Package name: ${name}`);
  console.log(`📦 Version: ${version}`);
  console.log(`🏷️  Expected tag: ${expectedTag}`);
  console.log(`🏷️  Actual tag: ${tag}`);

  // Validar se a tag corresponde
  if (tag === expectedTag) {
    console.log('✅ Tag validation successful!');
    process.exit(0);
  } else {
    console.error(`❌ Tag validation failed!`);
    console.error(`   Expected: ${expectedTag}`);
    console.error(`   Got: ${tag}`);
    console.error('');
    console.error('💡 To fix this:');
    console.error(
      `   1. Update the version in ${packageJsonPath} to match the tag`,
    );
    console.error(`   2. Or create a new tag: git tag ${expectedTag}`);
    process.exit(1);
  }
}

main();
