#!/bin/bash

# Script para validar tags não reconhecidas pelo pipeline
#
# Usage: ./scripts/validate-unknown-tag.sh <tag>
# Example: ./scripts/validate-unknown-tag.sh @lisa/unknown-project@1.0.0

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "\n${CYAN}═══════════════════════════════════════════════════════════════${NC}"
    echo -e "${CYAN}  $1${NC}"
    echo -e "${CYAN}═══════════════════════════════════════════════════════════════${NC}\n"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check arguments
if [ $# -lt 1 ]; then
    print_error "Usage: $0 <tag>"
    print_info "Example: $0 @lisa/unknown-project@1.0.0"
    exit 1
fi

TAG="$1"

print_header "VALIDAÇÃO DE TAG NÃO RECONHECIDA"

print_warning "Tag recebida: $TAG"
print_info "Esta tag não corresponde a nenhum projeto configurado no pipeline."

# Known projects with Docker support
KNOWN_PROJECTS=("@lisa/ai.api" "@lisa/twilio.api" "@lisa/smarters.api")

print_info "📋 Projetos configurados no pipeline:"
for project in "${KNOWN_PROJECTS[@]}"; do
    print_info "   • $project@*"
done

# Try to extract project name from tag
if [[ "$TAG" =~ ^@lisa/([^@]+)@(.+)$ ]]; then
    PROJECT_NAME="${BASH_REMATCH[1]}"
    TAG_VERSION="${BASH_REMATCH[2]}"
    
    print_info ""
    print_info "🔍 Análise da tag:"
    print_info "   • Projeto extraído: @lisa/$PROJECT_NAME"
    print_info "   • Versão extraída: $TAG_VERSION"
    
    # Check if project directory exists
    PROJECT_DIR="apps/$PROJECT_NAME"
    if [ -d "$PROJECT_DIR" ]; then
        print_success "✅ Diretório do projeto encontrado: $PROJECT_DIR"
        
        # Check if package.json exists
        PACKAGE_JSON="$PROJECT_DIR/package.json"
        if [ -f "$PACKAGE_JSON" ]; then
            print_success "✅ Package.json encontrado: $PACKAGE_JSON"
            
            # Get package info
            PACKAGE_NAME=$(node -p "require('./$PACKAGE_JSON').name" 2>/dev/null || echo "N/A")
            PACKAGE_VERSION=$(node -p "require('./$PACKAGE_JSON').version" 2>/dev/null || echo "N/A")
            
            print_info "   • Nome no package.json: $PACKAGE_NAME"
            print_info "   • Versão no package.json: $PACKAGE_VERSION"
            
            # Check if Dockerfile exists
            DOCKERFILE="$PROJECT_DIR/Dockerfile"
            if [ -f "$DOCKERFILE" ]; then
                print_success "✅ Dockerfile encontrado: $DOCKERFILE"
                print_warning "🚨 PROJETO PARECE VÁLIDO MAS NÃO ESTÁ CONFIGURADO NO PIPELINE!"
                
                print_info ""
                print_info "💡 Para adicionar este projeto ao pipeline:"
                print_info "   1. Edite bitbucket-pipelines.yml"
                print_info "   2. Adicione a seção:"
                print_info "      '@lisa/$PROJECT_NAME@*':"
                print_info "        - step:"
                print_info "            name: Build and Push $PROJECT_NAME Image"
                print_info "            script:"
                print_info "              - ./scripts/docker-build.sh \$BITBUCKET_TAG apps/$PROJECT_NAME push"
                print_info "            services:"
                print_info "              - docker"
            else
                print_error "❌ Dockerfile não encontrado: $DOCKERFILE"
                print_info "💡 Este projeto não suporta deploy via Docker."
            fi
        else
            print_error "❌ Package.json não encontrado: $PACKAGE_JSON"
        fi
    else
        print_error "❌ Diretório do projeto não encontrado: $PROJECT_DIR"
        print_info "💡 Verifique se o nome do projeto na tag está correto."
    fi
else
    print_error "❌ Formato de tag inválido: $TAG"
    print_info "💡 Formato esperado: @lisa/{projeto}@{versão}"
    print_info "   Exemplo: @lisa/ai.api@1.0.0"
fi

print_header "PRÓXIMOS PASSOS"

print_info "🔧 Opções disponíveis:"
print_info "   1. Corrigir o nome da tag se estiver incorreto"
print_info "   2. Adicionar o projeto ao pipeline se for válido"
print_info "   3. Remover a tag se foi criada por engano"
print_info ""
print_info "📚 Documentação:"
print_info "   • docs/DEPLOY_GUIDE.md - Guia completo de deploy"
print_info "   • docs/QUICK_DEPLOY_REFERENCE.md - Referência rápida"
print_info ""
print_info "🧪 Testar localmente:"
print_info "   pnpm test:tag-validation"

# Exit with error to fail the pipeline
print_error "Pipeline interrompida devido a tag não reconhecida."
exit 1
