{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@lisa/common": ["libs/common/src"], "@lisa/common/*": ["libs/common/src/*"], "@lisa/crm-sdk": ["libs/crm-sdk/src"], "@lisa/crm-sdk/*": ["libs/crm-sdk/src/*"], "@lisa/lead-workflow": ["libs/lead-workflow/src"], "@lisa/lead-workflow/*": ["libs/lead-workflow/src/*"], "@lisa/main-workflow": ["libs/main-workflow/src"], "@lisa/main-workflow/*": ["libs/main-workflow/src/*"], "@lisa/twenty-sdk": ["libs/twenty-sdk/src"], "@lisa/twenty-sdk/*": ["libs/twenty-sdk/src/*"], "@lisa/twenty-sdk/cjs": ["libs/twenty-sdk/src/cjs"], "@lisa/twenty-sdk/cjs/*": ["libs/twenty-sdk/src/cjs/*"], "@lisa/twenty-sdk/cjs/api": ["libs/twenty-sdk/src/cjs/api"]}}}