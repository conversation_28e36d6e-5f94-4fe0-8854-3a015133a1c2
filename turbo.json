{"$schema": "https://turbo.build/schema.json", "ui": "tui", "concurrency": "15", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "build/**"]}, "start": {"cache": false, "persistent": true}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "lint": {"dependsOn": ["^build"]}, "format": {"outputs": []}, "type-check": {"dependsOn": ["^build"]}}, "globalDependencies": ["**/.env.*local", "**/.env", "tsconfig.json"]}